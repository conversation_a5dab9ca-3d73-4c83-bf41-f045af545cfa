import { createStore } from 'vuex';
import viewer from '@cpn/viewer/store';
const iPortalUrl = 'http://' + window.location.hostname + ':8190'
const iServerlUrl = 'http://' + window.location.hostname + ':8090'
const tomcatUrl = 'http://' + window.location.hostname + ':8080'
// const fileApiBaseUrl = 'http://' + window.location.hostname + ':8090/jcFileUpload'
// const fileApiBaseUrl = 'http://' + window.location.hostname + ':8082'
const fileApiBaseUrl = 'http://**************:8090/jcFileUpload'
// const iPortalUrl = 'http://**************:8190'
// const iServerlUrl = 'http://**************:8090'
// const tomcatUrl = 'http://**************:8080'
export default createStore({
    state: () => ({
        iportalHostUrl: iPortalUrl,
        // iportalHostUrl: 'http://*************:8090',
        iserverHostUrl: iServerlUrl,
        // iserverHostUrl: 'http://*************:8190',
        // tomcatHostUrl: 'http://' + window.location.hostname + ':8080',
        tomcatHostUrl: tomcatUrl,
        fileApiBaseUrl: fileApiBaseUrl,
        layer_url: {
            s3mLayer: iServerlUrl + '/iserver/services/3D-JCQXS3M3_0/rest/realspace',
            s3mLayer2021: iServerlUrl + '/iserver/services/3D-jc2021_15cm/rest/realspace',
            image_url: tomcatUrl + "/TileServer/arcgis/rest/services/YX2023/MapServer",
            // BIMLayer: iServerlUrl + '/iserver/services/3D-BIMHeBing4490/rest/realspace',
            BIMLayer: iServerlUrl + '/iserver/services/3D-BIMS3M3_0/rest/realspace',
            BIMDataLayer: iServerlUrl + '/iserver/services/data-BIMS3M3_0/rest/data',
            BIMFINALLayer: iServerlUrl + '/iserver/services/3D-BIMFINAL/rest/realspace',
            BIMFINALDataLayer: iServerlUrl + '/iserver/services/data-BIMFINAL/rest/data',
            MAXLayer: iServerlUrl + '/iserver/services/3D-JCMAX3_0V2/rest/realspace',
            DZDataUrl: iServerlUrl + '/iserver/services/data-JianChang/rest/data',
            //模拟仿真白模测试数据
            BaimoS3MUrl: iServerlUrl + '/iserver/services/3D-MoNiJianZhuShengChang/rest/realspace',
            BaimoDataUrl: iServerlUrl + '/iserver/services/data-MoNiJianZhuShengChang/rest/data',
            //钻孔地图服务
            zk_map_Url: iServerlUrl + '/iserver/services/map-JiaoZhouWanKeChuangXinQu/rest/maps/钻孔',
            //钻孔数据服务
            zk_data_Url: iServerlUrl + '/iserver/services/data-JiaoZhouWanKeChuangXinQu/rest/data',
            history_img: 'https://service0.sdmap.gov.cn:8081/hisimage/weipian20031W?tk=9cac4b6f5d65f7ed6d02ab591073320d',
            //DEM服务
            dem_scene_url: 'http://*************:8090/iserver/services/3D-DEM/rest/realspace/datas/DEMcim2000q%40DataSource',
            //天地图控转发后影像服务
            tdt_img_url:
                'https://service.sdmap.gov.cn/tileservice/sdrasterpubmap?tk=8b0a701d721fe0ee8916e07e3783fcce',
            //天地图控转发后影像注记服务
            tdt_cia_url:
                'http://t0.tianditu.gov.cn/img_w/wmts?tk=9cac4b6f5d65f7ed6d02ab591073320d',
            //地质体数据服务
            qdportDZTDataUrl:
                iServerlUrl + '/iserver/services/data-JiaoZhouWanKeChuangXinQu/rest/data',
            tdt_ele_url:
                'http://t0.tianditu.gov.cn/vec_c/wmts?tk=9cac4b6f5d65f7ed6d02ab591073320d',
            tdt_ele_zhuji_url:
                'http://t0.tianditu.gov.cn/cva_c/wmts?tk=9cac4b6f5d65f7ed6d02ab591073320d',
            sdtdt_ele_url:
                'https://service.sdmap.gov.cn/tileservice/sdpubmap?tk=8b0a701d721fe0ee8916e07e3783fcce'
        },
        api_url: {
            get_monitorPointSearch: 'http://47.105.89.156:8090/zdhjc/plugin/zdhjc/getMonitorPointData.htm'
        },
        dzModelLayers: [],
        function_url: {
            uploadModelUrl: iServerlUrl + '/importModel/files/upload',
            modelServerUrl: iServerlUrl + '/model/',
            objModelUrl: iServerlUrl + '/objup/objupload',
            baseUrl: 'http://*************',
            local_uploadModelUrl: tomcatUrl + '/importModel/files/upload',
            local_modelServerUrl: tomcatUrl + '/model/',
            local_objModelUrl: tomcatUrl + '/objup/objupload',
            local_baseUrl: 'http://localhost'
        },
        layers: {
            worldimgLayer: null,
            qingxieLayer: null, //默认加载
            imageLayer: null, //默认加载
            eleLayer: null,
            image2024: null,
            eleZhujiLayer: null,
            blueLayer: null,
            jingmoLayer: null,
            baimoLayer: null,
            qdImageLayer: null,
            cimSpecialTree: null,
            //地质体
            sceneDZT: {},
            layerTreeState: [],
            //BIM-规划
            BIMLayers: null,
            //BIM-最终
            BIMFINALLayers: null,
            //MAX
            MAXLayers: null,
            //钻孔
            qdgZuanKongLayers: null,
            //DEM
            DEMLayer: null
        },
        filterProperty: {
            '青岛三调地类权属': [

            ],
        },
        layer_configs: {},

        models: {
            dzmodelsMap: {},//dzmodels对象数组
            picked_dzt_url: null,
            picked_dzt_datasourcename: null,
            picked_dzt_datasetname: null
        },
        modelSiteShow: false,
        modelSiteGeo: {
            type: '',
            data: null,
        },
        UIStyle: {
            //suwindow距顶部距离
            top: 130,
            //suwindow距左侧距离
            left: 8,
            //打开的panel数量
            panelCount: 1
        },
        buttonState: {
            geoglogySearchState: true
        },
        iserverDatasetName: "GXQCIMZRZY",//数据源名称全局
        //空间查询
        geoAnalysisResultShow: false,
        geoAnalysisResultFiledNames: [],
        queryResultTableData: {},
        //卷帘分析
        scrollShow: false,
        // 图例显示（all）
        LegendShow: false,
        //空间分析
        geoAnalysisResult: null,
        geoAnalysisResultShow: false,
        // 管线图例显示
        pipiLegendShow: false,
        // 钻孔图例显示
        zukongLegendShow: false,
        //自动化监测显示
        monitorPointSearchBubbleShow: false,
        //图例列表
        legendList: [],
        //图例f
        legends: [],
        //图层与图例的字段对应表
        layerFieldMap: {},
        //已打开图层
        openedLayers: [],
        scene3cmList: [],
        scene15cm2021List: null,
        scene1p5cmList: [],
        sceneWaterList: [],

        sceneDzList: [],
        sceneZkList: [],
        placeOpacity: 100,
        queryResultShow: false,

        //钻孔查询结果
        queryDrillingShow: false,
        queryDrillingData: {},

        //搜索结果
        searchData: [],
        searchShow: false,
        //打开查询
        openQuery: true,
        //概况是否显示
        portOverviewShow: false,

        //摄像头点击查询
        cameraSearchBubbleShow: false,
        cameraSearchData: null,
        //是否显示视频弹框
        ifVideoShow: false,
        //视频连接地址
        videoSrc: '',
        //自动化监测点位点击查询
        monitorPointSearchBubbleShow: false,
        monitorPointSearchData: null,
        //自动化监测点位列表
        monitorPointsArr: [],
        //实景三维2024和实景三维2023的最大可见高度
        s3mMaxVisibleAltitude: 5000,
        //工具箱边栏打开状态
        isShowToolContainer: true,
        //工具箱边栏打开状态
        isShowToolContainer: true,
        //实景三维场景图层列表
        sceneList: [],
        //专题数据的树根节点的id数组
        specialTreeRootIdArr: [],
        //统计分类
        statisticsResult: null,
        statisticsResultShow: false,
        //查询结果
        isTableDataShow: true,
        isQueryResultList: false,
        //全景
        qjDrawerStat: false,
        qjDrawerUrl: '',
        //专题地图点击得到的FeatureOnresult
        featureGetOnresult: null,
        //bubble弹框 layer的名称
        bubbleLayerName: '',

        //登录用户当前图层授权字段的
        currentLayerFieldNames: null,
        //专题地图点击得到的FeatureRes
        featureGetRes: null,
        ////专题地图点击得到的totalCount
        featureGetTotalCount: ref(0),
        //属性查询对话框
        propertyWindowShow: false,
        //
        propertyDIVLoading: false,
        //属性查询data
        clickPropertyData: [],
    }),
    getters: {
        getLegends(state) {
            return state.legends
        },
        getOpenedLayers(state) {
            return state.openedLayers
        },
        getUIStyleTop(state) {
            return state.UIStyle.top
        },
        getGeoAnalysisResult(state) {
            return state.geoAnalysisResult
        },
        getStatisticsResult(state) {
            return state.statisticsResult
        },
        getStatisticsResultShow(state) {
            return state.statisticsResultShow
        },
        getUIStyleLeft(state) {
            return state.UIStyle.left
        },
        getUIStylePanelCount(state) {
            return state.UIStyle.panelCount
        },
        //地质体查询状态
        getGeoglogySearchState(state) {
            return state.buttonState.geoglogySearchState
        },
        getpropertyWindowShowState(state) {
            return state.propertyWindowShow
        },
        getClickPropertyData(state) {
            return state.clickPropertyData
        },
        getBubbleLayerName(state) {
            return state.bubbleLayerName
        },
        getIfVideoShow(state) {
            return state.ifVideoShow
        },
        getVideoSrc(state) {
            return state.videoSrc
        },
        //自动化监测点位
        getMonitorPointsArr(state) {
            return state.monitorPointsArr
        },
        getLayerLegendFeildName(state) {
            return state.layerFieldMap
        },
    },

    mutations: {
        //地质体查询状态
        updateGeoglogySearchState(state, data) {
            state.buttonState.geoglogySearchState = data
        },
        updateModelSiteShow(state, data) {
            // const index = state.layers[layer.name]
            state.modelSiteShow = data;
        },
        updateModelSiteData(state, data) {
            state.modelSiteGeo = data;
        },
        updateScrollShow(state, data) {
            state.scrollShow = data
        },
        updateQueryResultShow(state, data) {
            // const index = state.layers[layer.name]
            state.queryResultShow = data;
        },
        updateFeoAnalysisResultShow(state, data) {
            state.geoAnalysisResultShow = data
        },
        updateGeoAnalysisResult(state, data) {
            state.geoAnalysisResult = data
        },
        updateQueryResultData(state, data) {
            state.queryResultTableData = data;
        },
        updateStatisticsResultShow(state, data) {
            state.statisticsResultShow = data
        },
        updateStatisticsResult(state, data) {
            state.statisticsResult = data
        },
        updateOpenQuery(state, data) {
            state.openQuery = data;
        },
        updateVideoShow(state, data) {
            state.ifVideoShow = data;
        },
        updateVideoSrc(state, data) {
            state.videoSrc = data;
        },
        //初始化搜索结果
        initSearchData(state, data) {
            state.searchShow = false;
            state.searchData = data;
        },
        //增加查询数据
        updateSearchData(state, data) {
            state.searchData = data;
            if (data.length > 0) {
                state.searchShow = true;
            } else {
                state.searchShow = false;
            }
        },
        updateDrillingData(state, data) {
            state.queryDrillingData = data;
            if (data.data.length > 0) {
                state.queryDrillingShow = true;
            } else {
                state.queryDrillingShow = false;
            }
        },
        /**
         *  //更改首页概况内容是否显示
         * @param {当前store的状态信息} state
         * @param {true/false，是否显示首页概况内容} data
         */
        updatePortOverviewShow(state, data) {
            state.portOverviewShow = data;
        },
        //自动化监测点位点击查询
        updateMonitorPointAttribute(state, data) {
            // console.log(data);
            state.monitorPointSearchData = data;
            if (state.monitorPointSearchData != null) {
                state.monitorPointSearchBubbleShow = true;
            } else {
                state.monitorPointSearchBubbleShow = false;
            }
        },
        //自动化监测点位列表
        updateMonitorPointsArr(state, data) {
            state.monitorPointsArr = data;
        },
        // 摄像头点位点击查询属性
        updateCameraAttribute(state, data) {
            state.cameraSearchData = data;
            if (state.cameraSearchData != null) {
                state.cameraSearchBubbleShow = true;
            } else {
                state.cameraSearchBubbleShow = false;
            }
        },
        //自动化监测点位点击查询
        updateMonitorPointAttribute(state, data) {
            console.log(data);
            state.monitorPointSearchData = data;
            if (state.monitorPointSearchData != null) {
                state.monitorPointSearchBubbleShow = true;
            } else {
                state.monitorPointSearchBubbleShow = false;
            }
        },
        addOpenedLayers(state, data) {
            let tempArr = state.openedLayers
            tempArr.push(data)
            let timpdata = JSON.parse(JSON.stringify(tempArr))
            state.openedLayers = timpdata
        },
        removeOpenedLayers(state, layerName) {
            let tempArr = state.openedLayers
            tempArr.map((item, index) => {
                if (item.name == layerName) {
                    tempArr.splice(index, 1)
                }
            })
            let timpdata = JSON.parse(JSON.stringify(tempArr))
            state.openedLayers = timpdata
        },
        removeAllOpenedLayers(state) {
            state.openedLayers = []
        },
        updateUIStylePanelCount(state, data) {
            state.UIStyle.panelCount = data
        },
        addLegend(state, data) {
            let tempArr = state.legends
            tempArr.unshift(data)
            let timpdata = JSON.parse(JSON.stringify(tempArr))
            state.legends = timpdata
        },
        removeLegend(state, layerName) {
            let tempArr = state.legends
            tempArr.map((item, index) => {
                if (item.layerName == layerName) {
                    tempArr.splice(index, 1)
                }
            })
            let timpdata = JSON.parse(JSON.stringify(tempArr))
            state.legends = timpdata
        },
        clearLegend(state, data) {
            state.legends = []
        },
        addLayerFieldMap(state, data) {
            state.layerFieldMap[data.layerName] = data.fieldName
        },
        updateSceneList(state, data) {
            state.sceneList = data;
        },
        updateZuKongLegendShow(state, data) {
            state.zukongLegendShow = data;
        },
        udpateShowToolContainer(state, data) {
            state.isShowToolContainer = data
        },
        udpateisTableDataShow(state, data) {
            state.isTableDataShow = data
        },
        udpateisQueryResultList(state, data) {
            state.isQueryResultList = data
        },
        updateQJDrawerStat(state, data) {
            state.qjDrawerStat = data
        },
        updateQJDrawerUrl(state, data) {
            state.qjDrawerUrl = data
        },
        updateBubbleLayerName(state, data) {
            state.bubbleLayerName = data
        },
        updateS3mMaxVisibleAltitude(state, data) {
            state.s3mMaxVisibleAltitude = data
        },
        updatePipiLegendShow(state, data) {
            state.pipiLegendShow = data;
        },
        updateLegendShow(state, data) {
            state.LegendShow = data;
        },
        pushToSpecialTreeRootIdArr(state, data) {
            state.specialTreeRootIdArr.push(data)
        },
        updateFeatureGetRes(state, data) {
            state.featureGetRes = data
        },
        updateFeatureGetOnresult(state, data) {
            state.featureGetOnresult = data
        },
        updateFeatureGetTotalCount(state, data) {
            state.featureGetTotalCount = data
        },
        //查询结果对话框
        updatepropertyWindowShowState(state, data) {
            state.propertyWindowShow = data
        },
        updatepropertyDIVLoadingState(state, data) {
            state.propertyDIVLoading = data
        },
        //查询结果数据
        updateClickPropertyData(state, data) {
            state.clickPropertyData = data
        },
        updateCurrentLayerFieldNames(state, data) {
            state.currentLayerFieldNames = data
        },
    },
    modules: {
        viewer,
    },
});