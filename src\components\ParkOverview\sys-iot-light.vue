<template>
  <div class="dashboard">
    <main class="dashboard-main">
      <!-- 左侧 -->
      <section class="dashboard-left">
        <div class="panel visual_box" v-if="ludeng_info[0]">
          <img
            src="/public/images/lbx.png"
            style="
              position: absolute;
              width: 350px;
              margin-left: 10%;
              opacity: 0.7;
              animation: myfirst 18s infinite linear;
            "
          />
          <div class="visual_title">
            <div class="zuo"></div>
            <h2>{{ ludeng_info[0].name }}</h2>
            <div class="you"></div>
            <img src="/public/images/ksh33.png" />
          </div>
          <el-row>
            <el-col :span="8">
              <el-row>
                <el-col :span="12" class="infoName">开关状态</el-col>
                <el-col
                  :span="12"
                  :class="[
                    'infoValue',
                    ludeng_info[0].detail?.ld?.dqkgztid === 2
                      ? 'infoValueRed'
                      : 'infoValueGreen',
                  ]"
                  >{{
                    ludeng_info[0].detail?.ld?.dqkgztid === 2 ? "关灯" : "开灯"
                  }}</el-col
                >
              </el-row>
              <el-row>
                <el-col :span="12" class="infoName">当前功率</el-col>
                <el-col :span="12" class="infoValue">{{
                  ludeng_info[0].detail?.ld?.dqgl || 0
                }}</el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="infoName">当前电压</el-col>
                <el-col :span="12" class="infoValue">{{
                  ludeng_info[0].detail?.ld?.dqdy || 0
                }}</el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="infoName">当前电流</el-col>
                <el-col :span="12" class="infoValue">{{
                  ludeng_info[0].detail?.ld?.dqdl || 0
                }}</el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="infoName">当前亮度</el-col>
                <el-col :span="12" class="infoValue">{{
                  ludeng_info[0].detail?.ld?.dqld || 0
                }}</el-col>
              </el-row>
            </el-col>
            <el-col :span="16">
              <el-row>
                <div class="yelloCircle"></div>
                <div style="margin-right: 10px">气象信息</div>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/wendu.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 60%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[0].detail?.qxz?.wd || 0 }}
                      </div>
                      <div class="ldInfoType">温度 ℃</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/shidu.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 100%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[0].detail?.qxz?.sd || 0 }}
                      </div>
                      <div class="ldInfoType">湿度 %</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row style="margin-top: 20px">
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/zaosheng.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 60%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[0].detail?.qxz?.zs || 0 }}
                      </div>
                      <div class="ldInfoType">噪声 dB</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/pm2.5.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 100%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[0].detail?.qxz?.pm25 || 0 }}
                      </div>
                      <div class="ldInfoType">PM2.5</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row style="margin-top: 20px">
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/pm10.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 60%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[0].detail?.qxz?.pm10 || 0 }}
                      </div>
                      <div class="ldInfoType">PM mg/m³</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/zhaodu.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 100%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[0].detail?.qxz?.zd || 0 }}
                      </div>
                      <div class="ldInfoType">照度 lx</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
        <div class="panel visual_box" v-if="ludeng_info[1]">
          <img
            src="/public/images/jt.png"
            style="
              position: absolute;
              width: 350px;
              margin-left: 10%;
              opacity: 0.5;
              animation: myfirst 10s infinite linear;
            "
          />
          <div class="visual_title">
            <div class="zuo"></div>
            <h2>{{ ludeng_info[1].name }}</h2>
            <img src="/public/images/ksh33.png" />
          </div>
          <el-row>
            <el-col :span="8">
              <el-row>
                <el-col :span="12" class="infoName">开关状态</el-col>
                <el-col
                  :span="12"
                  :class="[
                    'infoValue',
                    ludeng_info[1].detail?.ld?.dqkgztid === 2
                      ? 'infoValueRed'
                      : 'infoValueGreen',
                  ]"
                  >{{
                    ludeng_info[1].detail?.ld?.dqkgztid === 2 ? "关灯" : "开灯"
                  }}</el-col
                >
              </el-row>
              <el-row>
                <el-col :span="12" class="infoName">当前功率</el-col>
                <el-col :span="12" class="infoValue">{{
                  ludeng_info[1].detail?.ld?.dqgl || 0
                }}</el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="infoName">当前电压</el-col>
                <el-col :span="12" class="infoValue">{{
                  ludeng_info[1].detail?.ld?.dqdy || 0
                }}</el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="infoName">当前电流</el-col>
                <el-col :span="12" class="infoValue">{{
                  ludeng_info[1].detail?.ld?.dqdl || 0
                }}</el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="infoName">当前亮度</el-col>
                <el-col :span="12" class="infoValue">{{
                  ludeng_info[1].detail?.ld?.dqld || 0
                }}</el-col>
              </el-row>
            </el-col>
            <el-col :span="16">
              <el-row>
                <div class="yelloCircle"></div>
                <div style="margin-right: 10px">气象信息</div>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/wendu.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 60%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[1].detail?.qxz?.wd || 0 }}
                      </div>
                      <div class="ldInfoType">温度 ℃</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/shidu.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 100%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[1].detail?.qxz?.sd || 0 }}
                      </div>
                      <div class="ldInfoType">湿度 %</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row style="margin-top: 20px">
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/zaosheng.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 60%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[1].detail?.qxz?.zs || 0 }}
                      </div>
                      <div class="ldInfoType">噪声 dB</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/pm2.5.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 100%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[1].detail?.qxz?.pm25 || 0 }}
                      </div>
                      <div class="ldInfoType">PM mg/m³</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row style="margin-top: 20px">
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/pm10.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 60%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[1].detail?.qxz?.pm10 || 0 }}
                      </div>
                      <div class="ldInfoType">PM mg/m³</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/zhaodu.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 100%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[1].detail?.qxz?.zd || 0 }}
                      </div>
                      <div class="ldInfoType">照度 lx</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
      </section>
      <!-- 中间 -->
      <section class="dashboard-center">
        <div
          class="panel_center visual_box_center"
          style="width: 800px; position: relative; left: 0px"
          v-if="ludeng_info[2]"
        >
          <img src="/public/images/ksh33.png" />
          <img
            src="/public/images/lbx.png"
            style="
              position: absolute;
              width: 350px;
              margin-left: 10%;
              opacity: 0.7;
              animation: myfirst 18s infinite linear;
            "
          />
          <div class="visual_title">
            <div class="zuo"></div>
            <h2>{{ ludeng_info[2].name }}</h2>
            <div class="you"></div>
          </div>
          <el-row>
            <el-col :span="6">
              <el-row>
                <el-col :span="10" class="infoNameCenter">开关状态</el-col>
                <el-col
                  :span="10"
                  :class="[
                    'infoValueCenter',
                    ludeng_info[2].detail?.ld?.dqkgztid === 2
                      ? 'infoValueRed'
                      : 'infoValueGreen',
                  ]"
                  >{{
                    ludeng_info[2].detail?.ld?.dqkgztid === 2 ? "关灯" : "开灯"
                  }}</el-col
                >
              </el-row>
              <el-row>
                <el-col :span="10" class="infoNameCenter">当前功率</el-col>
                <el-col :span="10" class="infoValueCenter">{{
                  ludeng_info[2].detail?.ld?.dqgl || 0
                }}</el-col>
              </el-row>
              <el-row>
                <el-col :span="10" class="infoNameCenter">当前电压</el-col>
                <el-col :span="10" class="infoValueCenter">{{
                  ludeng_info[2].detail?.ld?.dqdy || 0
                }}</el-col>
              </el-row>
              <el-row>
                <el-col :span="10" class="infoNameCenter">当前电流</el-col>
                <el-col :span="10" class="infoValueCenter">{{
                  ludeng_info[2].detail?.ld?.dqdl || 0
                }}</el-col>
              </el-row>
              <el-row>
                <el-col :span="10" class="infoNameCenter">当前亮度</el-col>
                <el-col :span="10" class="infoValueCenter">{{
                  ludeng_info[2].detail?.ld?.dqld || 0
                }}</el-col>
              </el-row>
            </el-col>
            <el-col :span="18">
              <el-row>
                <div class="yelloCircle"></div>
                <div style="margin-right: 10px">气象信息</div>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <div class="qxDiv">
                    <img
                      src="/public/images/wendu.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 60%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[2].detail?.qxz?.wd || 0 }}
                      </div>
                      <div class="ldInfoType">温度 ℃</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="qxDiv">
                    <img
                      src="/public/images/shidu.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 100%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[2].detail?.qxz?.sd || 0 }}
                      </div>
                      <div class="ldInfoType">湿度 %</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="qxDiv">
                    <img
                      src="/public/images/zaosheng.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 60%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[2].detail?.qxz?.zs || 0 }}
                      </div>
                      <div class="ldInfoType">噪声 dB</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row style="margin-top: 20px">
                <el-col :span="8">
                  <div class="qxDiv">
                    <img
                      src="/public/images/pm2.5.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 100%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[2].detail?.qxz?.pm25 || 0 }}
                      </div>
                      <div class="ldInfoType">PM mg/m³</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="qxDiv">
                    <img
                      src="/public/images/pm10.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 60%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[2].detail?.qxz?.pm10 || 0 }}
                      </div>
                      <div class="ldInfoType">PM mg/m³</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="qxDiv">
                    <img
                      src="/public/images/zhaodu.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 100%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[2].detail?.qxz?.zd || 0 }}
                      </div>
                      <div class="ldInfoType">照度 lx</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
      </section>
      <!-- 右侧 -->
      <section class="dashboard-right">
        <div class="panel visual_box" v-if="ludeng_info[3]">
          <img
            src="/public/images/lbx.png"
            style="
              position: absolute;
              width: 350px;
              margin-left: 10%;
              opacity: 0.7;
              animation: myfirst 18s infinite linear;
            "
          />
          <div class="visual_title">
            <div class="zuo"></div>
            <h2>{{ ludeng_info[3].name }}</h2>
            <div class="you"></div>
            <img src="/public/images/ksh33.png" />
          </div>
          <el-row>
            <el-col :span="8">
              <el-row>
                <el-col :span="12" class="infoName">开关状态</el-col>
                <el-col
                  :span="12"
                  :class="[
                    'infoValue',
                    ludeng_info[3].detail?.ld?.dqkgztid === 2
                      ? 'infoValueRed'
                      : 'infoValueGreen',
                  ]"
                  >{{
                    ludeng_info[3].detail?.ld?.dqkgztid === 2 ? "关灯" : "开灯"
                  }}</el-col
                >
              </el-row>
              <el-row>
                <el-col :span="12" class="infoName">当前功率</el-col>
                <el-col :span="12" class="infoValue">{{
                  ludeng_info[3].detail?.ld?.dqgl || 0
                }}</el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="infoName">当前电压</el-col>
                <el-col :span="12" class="infoValue">{{
                  ludeng_info[3].detail?.ld?.dqdy || 0
                }}</el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="infoName">当前电流</el-col>
                <el-col :span="12" class="infoValue">{{
                  ludeng_info[3].detail?.ld?.dqdl || 0
                }}</el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="infoName">当前亮度</el-col>
                <el-col :span="12" class="infoValue">{{
                  ludeng_info[3].detail?.ld?.dqld || 0
                }}</el-col>
              </el-row>
            </el-col>
            <el-col :span="16">
              <el-row>
                <div class="yelloCircle"></div>
                <div style="margin-right: 10px">气象信息</div>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/wendu.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 60%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[3].detail?.qxz?.wd || 0 }}
                      </div>
                      <div class="ldInfoType">温度 ℃</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/shidu.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 100%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[3].detail?.qxz?.sd || 0 }}
                      </div>
                      <div class="ldInfoType">湿度 %</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row style="margin-top: 20px">
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/zaosheng.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 60%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[3].detail?.qxz?.zs || 0 }}
                      </div>
                      <div class="ldInfoType">噪声 dB</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/pm2.5.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 100%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[3].detail?.qxz?.pm25 || 0 }}
                      </div>
                      <div class="ldInfoType">PM mg/m³</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row style="margin-top: 20px">
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/pm10.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 60%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[3].detail?.qxz?.pm10 || 0 }}
                      </div>
                      <div class="ldInfoType">PM mg/m³</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/zhaodu.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 100%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[3].detail?.qxz?.zd || 0 }}
                      </div>
                      <div class="ldInfoType">照度 lx</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
        <div class="panel visual_box" v-if="ludeng_info[4]">
          <img
            src="/public/images/lbx.png"
            style="
              position: absolute;
              width: 350px;
              margin-left: 10%;
              opacity: 0.7;
              animation: myfirst 18s infinite linear;
            "
          />
          <div class="visual_title">
            <div class="zuo"></div>
            <h2>{{ ludeng_info[4].name }}</h2>
            <div class="you"></div>
            <img src="/public/images/ksh33.png" />
          </div>
          <el-row>
            <el-col :span="8">
              <el-row>
                <el-col :span="12" class="infoName">开关状态</el-col>
                <el-col
                  :span="12"
                  :class="[
                    'infoValue',
                    ludeng_info[4].detail?.ld?.dqkgztid === 2
                      ? 'infoValueRed'
                      : 'infoValueGreen',
                  ]"
                  >{{
                    ludeng_info[4].detail?.ld?.dqkgztid === 2 ? "关灯" : "开灯"
                  }}</el-col
                >
              </el-row>
              <el-row>
                <el-col :span="12" class="infoName">当前功率</el-col>
                <el-col :span="12" class="infoValue">{{
                  ludeng_info[4].detail?.ld?.dqgl || 0
                }}</el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="infoName">当前电压</el-col>
                <el-col :span="12" class="infoValue">{{
                  ludeng_info[4].detail?.ld?.dqdy || 0
                }}</el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="infoName">当前电流</el-col>
                <el-col :span="12" class="infoValue">{{
                  ludeng_info[4].detail?.ld?.dqdl || 0
                }}</el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="infoName">当前亮度</el-col>
                <el-col :span="12" class="infoValue">{{
                  ludeng_info[4].detail?.ld?.dqld || 0
                }}</el-col>
              </el-row>
            </el-col>
            <el-col :span="16">
              <el-row>
                <div class="yelloCircle"></div>
                <div style="margin-right: 10px">气象信息</div>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/wendu.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 60%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[4].detail?.qxz?.wd || 0 }}
                      </div>
                      <div class="ldInfoType">温度 ℃</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/shidu.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 100%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[4].detail?.qxz?.sd || 0 }}
                      </div>
                      <div class="ldInfoType">湿度 %</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row style="margin-top: 20px">
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/zaosheng.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 60%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[4].detail?.qxz?.zs || 0 }}
                      </div>
                      <div class="ldInfoType">噪声 dB</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/pm2.5.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 100%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[4].detail?.qxz?.pm25 || 0 }}
                      </div>
                      <div class="ldInfoType">PM mg/m³</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row style="margin-top: 20px">
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/pm10.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 60%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[4].detail?.qxz?.pm10 || 0 }}
                      </div>
                      <div class="ldInfoType">PM mg/m³</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="qxDiv">
                    <img
                      src="/public/images/zhaodu.png"
                      alt=""
                      class="ldInfoImg"
                    />
                    <div style="width: 100%; height: 100%">
                      <div class="ldInfoValue">
                        {{ ludeng_info[4].detail?.qxz?.zd || 0 }}
                      </div>
                      <div class="ldInfoType">照度 lx</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
      </section>
    </main>

    <!-- 视频对话框 -->
    <SuWindow
      :title="videoDialog.title"
      :show="videoDialog.show"
      :id="videoDialog.id"
      class="video-dialog"
      width="800px"
      height="600px"
      @close="handleVideoDialogClose"
    >
      <div class="video-container">
        <video
          id="cameraVideoPlayer"
          class="video-player"
          controls
          autoplay
          muted
        ></video>
      </div>
    </SuWindow>
  </div>
</template>
  
<script setup>
import { ref, onMounted, nextTick, onUnmounted, provide, watch } from "vue";
import VChart from "vue-echarts";

// 定义props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
import * as echarts from "echarts";
import "echarts-liquidfill";
import cameraData from "@/components/Iot/camera.json";
import emitter from "@/utils/mitt.js";

const scrolltimer = ref(null);
const ludeng_info = ref([
  {
    code: "00100601",
    name: "智慧灯杆位置西",
    jzzcid: 1,
    azwz: "楼山创亿空间01",
    jd: 120.37720338245066,
    wd: 36.20433974959927,
    zhtxsj: "Jun 26, 2025 10:25:36 AM",
    createuserid: "1",
    createusername: "开发管理员",
    createtime: "Sep 19, 2024 2:26:00 PM",
    id: 4449,
    detail: {
      code: "00100601",
      name: "智慧灯杆位置西",
      ckzclxid: 115,
      parentid: 4448,
      parentname: "智慧灯杆服务",
      jzzcid: 1,
      jzzcname: "楼山创亿空间",
      azwz: "楼山创亿空间01",
      jd: 120.3888433696935,
      wd: 36.21064456882144,
      csid: 1,
      zhtxsj: "Jun 26, 2025 10:30:14 AM",
      createuserid: "1",
      createusername: "开发管理员",
      createtime: "Sep 19, 2024 2:26:00 PM",
      memo: null,
      id: 4449,
      gzmsid: null,
      csfsid: null,
      fwqport: null,
      fwqip: null,
      dhcp: 0,
      bdip: null,
      zwym: null,
      wgip: null,
      wg: {
        code: "001007",
        name: "灯杆网关位置西",
        ckzclxid: 18,
        parentid: 1,
        parentname: "测控组成",
        jzzcid: 1,
        jzzcname: "楼山创亿空间",
        azwz: null,
        jd: null,
        wd: null,
        csid: 1,
        zhtxsj: "Jun 26, 2025 10:30:14 AM",
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Sep 24, 2024 2:44:33 PM",
        memo: null,
        id: 4454,
        uploadinterval: null,
        net_nwip: "************",
        net_nwport: 9998,
        net_nwwebport: null,
        net_wwip: "**************",
        net_wwport: 9998,
        net_wwwebport: null,
        dgid: 4449,
        dgname: "智慧灯杆位置西",
      },
      ld: {
        code: "00100701",
        name: "路灯1",
        ckzclxid: 116,
        parentid: 4454,
        parentname: "灯杆网关位置西",
        jzzcid: 1,
        jzzcname: "楼山创亿空间",
        azwz: null,
        jd: null,
        wd: null,
        csid: 1,
        zhtxsj: "Jun 26, 2025 10:30:14 AM",
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Sep 24, 2024 2:44:33 PM",
        memo: null,
        id: 4459,
        jhdztsbpl: 60,
        dqgdztid: null,
        dqgdlylxid: null,
        dqkgztid: 2,
        dqkqsj: "Oct 16, 2024 7:16:05 PM",
        dqld: 0.0,
        dqgl: 92.0,
        dqdy: 1415.0,
        dqdl: 24.0,
        zhcysj: "Jun 26, 2025 10:29:26 AM",
        sckqsj: "Oct 12, 2024 11:00:25 AM",
        scgbsj: "Oct 12, 2024 11:21:00 AM",
        dgid: 4449,
        dgname: "智慧灯杆位置西",
        kqsk: "18:20",
        gbsk: "06:10",
      },
      qxz: {
        code: "00100702",
        name: "气象站1",
        ckzclxid: 117,
        parentid: 4454,
        parentname: "灯杆网关位置西",
        jzzcid: 1,
        jzzcname: "楼山创亿空间",
        azwz: null,
        jingdu: null,
        weidu: null,
        csid: 1,
        zhtxsj: "Jun 26, 2025 10:30:14 AM",
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Sep 24, 2024 2:44:33 PM",
        memo: "备注",
        id: 4460,
        jhdztsbpl: 60,
        wd: 26.9,
        sd: 68.9,
        zs: 67.3,
        pm25: 63.0,
        pm10: 90.0,
        zd: 47488.0,
        zddjid: 5,
        qy: 0.0,
        zhcysj: "Jun 26, 2025 10:29:33 AM",
        dgid: 4449,
        dgname: "智慧灯杆位置西",
        sdlevel: 1,
        wdlevel: 1,
        zslevel: 1,
        pm25level: 1,
        pm10level: 3,
        zdlevel: 3,
        qylevel: 1,
      },
      xsp: {
        code: "xsp001",
        name: "显示屏位置西",
        ckzclxid: 23,
        parentid: 1,
        parentname: "测控组成",
        jzzcid: null,
        jzzcname: null,
        azwz: null,
        jd: null,
        wd: null,
        csid: 1,
        zhtxsj: null,
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Oct 9, 2024 5:27:31 PM",
        memo: null,
        id: 4475,
        kzztid: null,
        kzztms: null,
        xskd: null,
        xsgd: null,
        xsscid: null,
        xsscms: null,
        xshdid: null,
        isupdate: null,
        isupdatetz: null,
        ip: "************",
        port: 80,
        kzfsid: null,
        xcbyjmsj: null,
        dqfqid: null,
        direction: null,
        content: null,
        lastpublishtime: null,
        dgid: 4449,
        dgname: "智慧灯杆位置西",
        fileurl: "group1/M00/12/CF/wKgB6mhcsKiAdqlQAAAFk15FqYw734.jpg",
      },
      sxj: {
        code: "sxj001",
        name: "摄像机位置西",
        ckzclxid: 16,
        parentid: 1,
        parentname: "测控组成",
        jzzcid: null,
        jzzcname: null,
        azwz: null,
        jd: null,
        wd: null,
        zhtxsj: null,
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Feb 17, 2025 1:12:24 PM",
        memo: null,
        id: 4484,
        nwip: null,
        nwport: null,
        nwwebport: null,
        wwip: null,
        wwport: null,
        wwwebport: null,
        username: null,
        password: null,
        gbcode: "41010500001320000702",
        gbtdcode: "41010500001320000712",
        td: 1,
        csid: 1,
        ws: null,
        sbmc: null,
        sfjl: null,
        jlsbip: null,
        jlsbtd: null,
        zcdw: null,
        sddw: null,
        dwnfz: null,
        canloginstate: 2,
        lastlogintime: null,
        protocol: 4,
        nvrid: null,
        indexcode: null,
        jcfxid: null,
        dgid: 4449,
        dgname: "智慧灯杆位置西",
      },
    },
  },
  {
    code: "00100602",
    name: "智慧灯杆位置东",
    jzzcname: "楼山创亿空间",
    azwz: "山东省青岛市李沧区四流北路78号",
    jd: 120.37868033693988,
    wd: 36.20482608050773,
    zhtxsj: "Jun 26, 2025 10:25:36 AM",
    createuserid: "1",
    createusername: "开发管理员",
    createtime: "Sep 19, 2024 2:26:00 PM",
    id: 4450,
    detail: {
      code: "00100601",
      name: "智慧灯杆位置西",
      ckzclxid: 115,
      parentid: 4448,
      parentname: "智慧灯杆服务",
      jzzcid: 1,
      jzzcname: "楼山创亿空间",
      azwz: "楼山创亿空间01",
      jd: 120.3888433696935,
      wd: 36.21064456882144,
      csid: 1,
      zhtxsj: "Jun 26, 2025 10:30:14 AM",
      createuserid: "1",
      createusername: "开发管理员",
      createtime: "Sep 19, 2024 2:26:00 PM",
      memo: null,
      id: 4449,
      gzmsid: null,
      csfsid: null,
      fwqport: null,
      fwqip: null,
      dhcp: 0,
      bdip: null,
      zwym: null,
      wgip: null,
      wg: {
        code: "001007",
        name: "灯杆网关位置西",
        ckzclxid: 18,
        parentid: 1,
        parentname: "测控组成",
        jzzcid: 1,
        jzzcname: "楼山创亿空间",
        azwz: null,
        jd: null,
        wd: null,
        csid: 1,
        zhtxsj: "Jun 26, 2025 10:30:14 AM",
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Sep 24, 2024 2:44:33 PM",
        memo: null,
        id: 4454,
        uploadinterval: null,
        net_nwip: "************",
        net_nwport: 9998,
        net_nwwebport: null,
        net_wwip: "**************",
        net_wwport: 9998,
        net_wwwebport: null,
        dgid: 4449,
        dgname: "智慧灯杆位置西",
      },
      ld: {
        code: "00100701",
        name: "路灯1",
        ckzclxid: 116,
        parentid: 4454,
        parentname: "灯杆网关位置西",
        jzzcid: 1,
        jzzcname: "楼山创亿空间",
        azwz: null,
        jd: null,
        wd: null,
        csid: 1,
        zhtxsj: "Jun 26, 2025 10:30:14 AM",
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Sep 24, 2024 2:44:33 PM",
        memo: null,
        id: 4459,
        jhdztsbpl: 60,
        dqgdztid: null,
        dqgdlylxid: null,
        dqkgztid: 2,
        dqkqsj: "Oct 16, 2024 7:16:05 PM",
        dqld: 0.0,
        dqgl: 92.0,
        dqdy: 1415.0,
        dqdl: 24.0,
        zhcysj: "Jun 26, 2025 10:29:26 AM",
        sckqsj: "Oct 12, 2024 11:00:25 AM",
        scgbsj: "Oct 12, 2024 11:21:00 AM",
        dgid: 4449,
        dgname: "智慧灯杆位置西",
        kqsk: "18:20",
        gbsk: "06:10",
      },
      qxz: {
        code: "00100702",
        name: "气象站1",
        ckzclxid: 117,
        parentid: 4454,
        parentname: "灯杆网关位置西",
        jzzcid: 1,
        jzzcname: "楼山创亿空间",
        azwz: null,
        jingdu: null,
        weidu: null,
        csid: 1,
        zhtxsj: "Jun 26, 2025 10:30:14 AM",
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Sep 24, 2024 2:44:33 PM",
        memo: "备注",
        id: 4460,
        jhdztsbpl: 60,
        wd: 26.9,
        sd: 68.9,
        zs: 67.3,
        pm25: 63.0,
        pm10: 90.0,
        zd: 47488.0,
        zddjid: 5,
        qy: 0.0,
        zhcysj: "Jun 26, 2025 10:29:33 AM",
        dgid: 4449,
        dgname: "智慧灯杆位置西",
        sdlevel: 1,
        wdlevel: 1,
        zslevel: 1,
        pm25level: 1,
        pm10level: 3,
        zdlevel: 3,
        qylevel: 1,
      },
      xsp: {
        code: "xsp001",
        name: "显示屏位置西",
        ckzclxid: 23,
        parentid: 1,
        parentname: "测控组成",
        jzzcid: null,
        jzzcname: null,
        azwz: null,
        jd: null,
        wd: null,
        csid: 1,
        zhtxsj: null,
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Oct 9, 2024 5:27:31 PM",
        memo: null,
        id: 4475,
        kzztid: null,
        kzztms: null,
        xskd: null,
        xsgd: null,
        xsscid: null,
        xsscms: null,
        xshdid: null,
        isupdate: null,
        isupdatetz: null,
        ip: "************",
        port: 80,
        kzfsid: null,
        xcbyjmsj: null,
        dqfqid: null,
        direction: null,
        content: null,
        lastpublishtime: null,
        dgid: 4449,
        dgname: "智慧灯杆位置西",
        fileurl: "group1/M00/12/CF/wKgB6mhcsKiAdqlQAAAFk15FqYw734.jpg",
      },
      sxj: {
        code: "sxj001",
        name: "摄像机位置西",
        ckzclxid: 16,
        parentid: 1,
        parentname: "测控组成",
        jzzcid: null,
        jzzcname: null,
        azwz: null,
        jd: null,
        wd: null,
        zhtxsj: null,
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Feb 17, 2025 1:12:24 PM",
        memo: null,
        id: 4484,
        nwip: null,
        nwport: null,
        nwwebport: null,
        wwip: null,
        wwport: null,
        wwwebport: null,
        username: null,
        password: null,
        gbcode: "41010500001320000702",
        gbtdcode: "41010500001320000712",
        td: 1,
        csid: 1,
        ws: null,
        sbmc: null,
        sfjl: null,
        jlsbip: null,
        jlsbtd: null,
        zcdw: null,
        sddw: null,
        dwnfz: null,
        canloginstate: 2,
        lastlogintime: null,
        protocol: 4,
        nvrid: null,
        indexcode: null,
        jcfxid: null,
        dgid: 4449,
        dgname: "智慧灯杆位置西",
      },
    },
  },
  {
    code: "00100603",
    name: "智慧灯杆位置中",
    jzzcname: "楼山创亿空间",
    azwz: "山东省青岛市李沧区四流北路78号",
    jd: 120.37818928943997,
    wd: 36.2048903983935,
    zhtxsj: "Jun 26, 2025 10:25:36 AM",
    createuserid: "1",
    createusername: "开发管理员",
    createtime: "Sep 19, 2024 2:26:00 PM",
    id: 4451,
    detail: {
      code: "00100601",
      name: "智慧灯杆位置西",
      ckzclxid: 115,
      parentid: 4448,
      parentname: "智慧灯杆服务",
      jzzcid: 1,
      jzzcname: "楼山创亿空间",
      azwz: "楼山创亿空间01",
      jd: 120.3888433696935,
      wd: 36.21064456882144,
      csid: 1,
      zhtxsj: "Jun 26, 2025 10:30:14 AM",
      createuserid: "1",
      createusername: "开发管理员",
      createtime: "Sep 19, 2024 2:26:00 PM",
      memo: null,
      id: 4449,
      gzmsid: null,
      csfsid: null,
      fwqport: null,
      fwqip: null,
      dhcp: 0,
      bdip: null,
      zwym: null,
      wgip: null,
      wg: {
        code: "001007",
        name: "灯杆网关位置西",
        ckzclxid: 18,
        parentid: 1,
        parentname: "测控组成",
        jzzcid: 1,
        jzzcname: "楼山创亿空间",
        azwz: null,
        jd: null,
        wd: null,
        csid: 1,
        zhtxsj: "Jun 26, 2025 10:30:14 AM",
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Sep 24, 2024 2:44:33 PM",
        memo: null,
        id: 4454,
        uploadinterval: null,
        net_nwip: "************",
        net_nwport: 9998,
        net_nwwebport: null,
        net_wwip: "**************",
        net_wwport: 9998,
        net_wwwebport: null,
        dgid: 4449,
        dgname: "智慧灯杆位置西",
      },
      ld: {
        code: "00100701",
        name: "路灯1",
        ckzclxid: 116,
        parentid: 4454,
        parentname: "灯杆网关位置西",
        jzzcid: 1,
        jzzcname: "楼山创亿空间",
        azwz: null,
        jd: null,
        wd: null,
        csid: 1,
        zhtxsj: "Jun 26, 2025 10:30:14 AM",
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Sep 24, 2024 2:44:33 PM",
        memo: null,
        id: 4459,
        jhdztsbpl: 60,
        dqgdztid: null,
        dqgdlylxid: null,
        dqkgztid: 2,
        dqkqsj: "Oct 16, 2024 7:16:05 PM",
        dqld: 0.0,
        dqgl: 92.0,
        dqdy: 1415.0,
        dqdl: 24.0,
        zhcysj: "Jun 26, 2025 10:29:26 AM",
        sckqsj: "Oct 12, 2024 11:00:25 AM",
        scgbsj: "Oct 12, 2024 11:21:00 AM",
        dgid: 4449,
        dgname: "智慧灯杆位置西",
        kqsk: "18:20",
        gbsk: "06:10",
      },
      qxz: {
        code: "00100702",
        name: "气象站1",
        ckzclxid: 117,
        parentid: 4454,
        parentname: "灯杆网关位置西",
        jzzcid: 1,
        jzzcname: "楼山创亿空间",
        azwz: null,
        jingdu: null,
        weidu: null,
        csid: 1,
        zhtxsj: "Jun 26, 2025 10:30:14 AM",
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Sep 24, 2024 2:44:33 PM",
        memo: "备注",
        id: 4460,
        jhdztsbpl: 60,
        wd: 26.9,
        sd: 68.9,
        zs: 67.3,
        pm25: 63.0,
        pm10: 90.0,
        zd: 47488.0,
        zddjid: 5,
        qy: 0.0,
        zhcysj: "Jun 26, 2025 10:29:33 AM",
        dgid: 4449,
        dgname: "智慧灯杆位置西",
        sdlevel: 1,
        wdlevel: 1,
        zslevel: 1,
        pm25level: 1,
        pm10level: 3,
        zdlevel: 3,
        qylevel: 1,
      },
      xsp: {
        code: "xsp001",
        name: "显示屏位置西",
        ckzclxid: 23,
        parentid: 1,
        parentname: "测控组成",
        jzzcid: null,
        jzzcname: null,
        azwz: null,
        jd: null,
        wd: null,
        csid: 1,
        zhtxsj: null,
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Oct 9, 2024 5:27:31 PM",
        memo: null,
        id: 4475,
        kzztid: null,
        kzztms: null,
        xskd: null,
        xsgd: null,
        xsscid: null,
        xsscms: null,
        xshdid: null,
        isupdate: null,
        isupdatetz: null,
        ip: "************",
        port: 80,
        kzfsid: null,
        xcbyjmsj: null,
        dqfqid: null,
        direction: null,
        content: null,
        lastpublishtime: null,
        dgid: 4449,
        dgname: "智慧灯杆位置西",
        fileurl: "group1/M00/12/CF/wKgB6mhcsKiAdqlQAAAFk15FqYw734.jpg",
      },
      sxj: {
        code: "sxj001",
        name: "摄像机位置西",
        ckzclxid: 16,
        parentid: 1,
        parentname: "测控组成",
        jzzcid: null,
        jzzcname: null,
        azwz: null,
        jd: null,
        wd: null,
        zhtxsj: null,
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Feb 17, 2025 1:12:24 PM",
        memo: null,
        id: 4484,
        nwip: null,
        nwport: null,
        nwwebport: null,
        wwip: null,
        wwport: null,
        wwwebport: null,
        username: null,
        password: null,
        gbcode: "41010500001320000702",
        gbtdcode: "41010500001320000712",
        td: 1,
        csid: 1,
        ws: null,
        sbmc: null,
        sfjl: null,
        jlsbip: null,
        jlsbtd: null,
        zcdw: null,
        sddw: null,
        dwnfz: null,
        canloginstate: 2,
        lastlogintime: null,
        protocol: 4,
        nvrid: null,
        indexcode: null,
        jcfxid: null,
        dgid: 4449,
        dgname: "智慧灯杆位置西",
      },
    },
  },
  {
    code: "00100604",
    name: "智慧灯杆位置南",
    jzzcname: "楼山创亿空间",
    azwz: "山东省青岛市李沧区四流北路78号",
    jd: 120.37816365084426,
    wd: 36.20412387069241,
    zhtxsj: "Jun 19, 2025 8:24:59 PM",
    createuserid: "1",
    createusername: "开发管理员",
    createtime: "Sep 19, 2024 2:26:00 PM",
    id: 4452,
    detail: {
      code: "00100601",
      name: "智慧灯杆位置西",
      ckzclxid: 115,
      parentid: 4448,
      parentname: "智慧灯杆服务",
      jzzcid: 1,
      jzzcname: "楼山创亿空间",
      azwz: "楼山创亿空间01",
      jd: 120.3888433696935,
      wd: 36.21064456882144,
      csid: 1,
      zhtxsj: "Jun 26, 2025 10:30:14 AM",
      createuserid: "1",
      createusername: "开发管理员",
      createtime: "Sep 19, 2024 2:26:00 PM",
      memo: null,
      id: 4449,
      gzmsid: null,
      csfsid: null,
      fwqport: null,
      fwqip: null,
      dhcp: 0,
      bdip: null,
      zwym: null,
      wgip: null,
      wg: {
        code: "001007",
        name: "灯杆网关位置西",
        ckzclxid: 18,
        parentid: 1,
        parentname: "测控组成",
        jzzcid: 1,
        jzzcname: "楼山创亿空间",
        azwz: null,
        jd: null,
        wd: null,
        csid: 1,
        zhtxsj: "Jun 26, 2025 10:30:14 AM",
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Sep 24, 2024 2:44:33 PM",
        memo: null,
        id: 4454,
        uploadinterval: null,
        net_nwip: "************",
        net_nwport: 9998,
        net_nwwebport: null,
        net_wwip: "**************",
        net_wwport: 9998,
        net_wwwebport: null,
        dgid: 4449,
        dgname: "智慧灯杆位置西",
      },
      ld: {
        code: "00100701",
        name: "路灯1",
        ckzclxid: 116,
        parentid: 4454,
        parentname: "灯杆网关位置西",
        jzzcid: 1,
        jzzcname: "楼山创亿空间",
        azwz: null,
        jd: null,
        wd: null,
        csid: 1,
        zhtxsj: "Jun 26, 2025 10:30:14 AM",
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Sep 24, 2024 2:44:33 PM",
        memo: null,
        id: 4459,
        jhdztsbpl: 60,
        dqgdztid: null,
        dqgdlylxid: null,
        dqkgztid: 2,
        dqkqsj: "Oct 16, 2024 7:16:05 PM",
        dqld: 0.0,
        dqgl: 92.0,
        dqdy: 1415.0,
        dqdl: 24.0,
        zhcysj: "Jun 26, 2025 10:29:26 AM",
        sckqsj: "Oct 12, 2024 11:00:25 AM",
        scgbsj: "Oct 12, 2024 11:21:00 AM",
        dgid: 4449,
        dgname: "智慧灯杆位置西",
        kqsk: "18:20",
        gbsk: "06:10",
      },
      qxz: {
        code: "00100702",
        name: "气象站1",
        ckzclxid: 117,
        parentid: 4454,
        parentname: "灯杆网关位置西",
        jzzcid: 1,
        jzzcname: "楼山创亿空间",
        azwz: null,
        jingdu: null,
        weidu: null,
        csid: 1,
        zhtxsj: "Jun 26, 2025 10:30:14 AM",
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Sep 24, 2024 2:44:33 PM",
        memo: "备注",
        id: 4460,
        jhdztsbpl: 60,
        wd: 26.9,
        sd: 68.9,
        zs: 67.3,
        pm25: 63.0,
        pm10: 90.0,
        zd: 47488.0,
        zddjid: 5,
        qy: 0.0,
        zhcysj: "Jun 26, 2025 10:29:33 AM",
        dgid: 4449,
        dgname: "智慧灯杆位置西",
        sdlevel: 1,
        wdlevel: 1,
        zslevel: 1,
        pm25level: 1,
        pm10level: 3,
        zdlevel: 3,
        qylevel: 1,
      },
      xsp: {
        code: "xsp001",
        name: "显示屏位置西",
        ckzclxid: 23,
        parentid: 1,
        parentname: "测控组成",
        jzzcid: null,
        jzzcname: null,
        azwz: null,
        jd: null,
        wd: null,
        csid: 1,
        zhtxsj: null,
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Oct 9, 2024 5:27:31 PM",
        memo: null,
        id: 4475,
        kzztid: null,
        kzztms: null,
        xskd: null,
        xsgd: null,
        xsscid: null,
        xsscms: null,
        xshdid: null,
        isupdate: null,
        isupdatetz: null,
        ip: "************",
        port: 80,
        kzfsid: null,
        xcbyjmsj: null,
        dqfqid: null,
        direction: null,
        content: null,
        lastpublishtime: null,
        dgid: 4449,
        dgname: "智慧灯杆位置西",
        fileurl: "group1/M00/12/CF/wKgB6mhcsKiAdqlQAAAFk15FqYw734.jpg",
      },
      sxj: {
        code: "sxj001",
        name: "摄像机位置西",
        ckzclxid: 16,
        parentid: 1,
        parentname: "测控组成",
        jzzcid: null,
        jzzcname: null,
        azwz: null,
        jd: null,
        wd: null,
        zhtxsj: null,
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Feb 17, 2025 1:12:24 PM",
        memo: null,
        id: 4484,
        nwip: null,
        nwport: null,
        nwwebport: null,
        wwip: null,
        wwport: null,
        wwwebport: null,
        username: null,
        password: null,
        gbcode: "41010500001320000702",
        gbtdcode: "41010500001320000712",
        td: 1,
        csid: 1,
        ws: null,
        sbmc: null,
        sfjl: null,
        jlsbip: null,
        jlsbtd: null,
        zcdw: null,
        sddw: null,
        dwnfz: null,
        canloginstate: 2,
        lastlogintime: null,
        protocol: 4,
        nvrid: null,
        indexcode: null,
        jcfxid: null,
        dgid: 4449,
        dgname: "智慧灯杆位置西",
      },
    },
  },
  {
    code: "00100605",
    name: "智慧灯杆位置北",
    jzzcname: "楼山创亿空间",
    azwz: "山东省青岛市李沧区四流北路78号",
    jd: 120.3787888925826,
    wd: 36.20547049763052,
    zhtxsj: "Jun 26, 2025 10:25:36 AM",
    createuserid: "1",
    createusername: "开发管理员",
    createtime: "Sep 19, 2024 2:26:00 PM",
    id: 4453,
    detail: {
      code: "00100601",
      name: "智慧灯杆位置西",
      ckzclxid: 115,
      parentid: 4448,
      parentname: "智慧灯杆服务",
      jzzcid: 1,
      jzzcname: "楼山创亿空间",
      azwz: "楼山创亿空间01",
      jd: 120.3888433696935,
      wd: 36.21064456882144,
      csid: 1,
      zhtxsj: "Jun 26, 2025 10:30:14 AM",
      createuserid: "1",
      createusername: "开发管理员",
      createtime: "Sep 19, 2024 2:26:00 PM",
      memo: null,
      id: 4449,
      gzmsid: null,
      csfsid: null,
      fwqport: null,
      fwqip: null,
      dhcp: 0,
      bdip: null,
      zwym: null,
      wgip: null,
      wg: {
        code: "001007",
        name: "灯杆网关位置西",
        ckzclxid: 18,
        parentid: 1,
        parentname: "测控组成",
        jzzcid: 1,
        jzzcname: "楼山创亿空间",
        azwz: null,
        jd: null,
        wd: null,
        csid: 1,
        zhtxsj: "Jun 26, 2025 10:30:14 AM",
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Sep 24, 2024 2:44:33 PM",
        memo: null,
        id: 4454,
        uploadinterval: null,
        net_nwip: "************",
        net_nwport: 9998,
        net_nwwebport: null,
        net_wwip: "**************",
        net_wwport: 9998,
        net_wwwebport: null,
        dgid: 4449,
        dgname: "智慧灯杆位置西",
      },
      ld: {
        code: "00100701",
        name: "路灯1",
        ckzclxid: 116,
        parentid: 4454,
        parentname: "灯杆网关位置西",
        jzzcid: 1,
        jzzcname: "楼山创亿空间",
        azwz: null,
        jd: null,
        wd: null,
        csid: 1,
        zhtxsj: "Jun 26, 2025 10:30:14 AM",
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Sep 24, 2024 2:44:33 PM",
        memo: null,
        id: 4459,
        jhdztsbpl: 60,
        dqgdztid: null,
        dqgdlylxid: null,
        dqkgztid: 2,
        dqkqsj: "Oct 16, 2024 7:16:05 PM",
        dqld: 0.0,
        dqgl: 92.0,
        dqdy: 1415.0,
        dqdl: 24.0,
        zhcysj: "Jun 26, 2025 10:29:26 AM",
        sckqsj: "Oct 12, 2024 11:00:25 AM",
        scgbsj: "Oct 12, 2024 11:21:00 AM",
        dgid: 4449,
        dgname: "智慧灯杆位置西",
        kqsk: "18:20",
        gbsk: "06:10",
      },
      qxz: {
        code: "00100702",
        name: "气象站1",
        ckzclxid: 117,
        parentid: 4454,
        parentname: "灯杆网关位置西",
        jzzcid: 1,
        jzzcname: "楼山创亿空间",
        azwz: null,
        jingdu: null,
        weidu: null,
        csid: 1,
        zhtxsj: "Jun 26, 2025 10:30:14 AM",
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Sep 24, 2024 2:44:33 PM",
        memo: "备注",
        id: 4460,
        jhdztsbpl: 60,
        wd: 26.9,
        sd: 68.9,
        zs: 67.3,
        pm25: 63.0,
        pm10: 90.0,
        zd: 47488.0,
        zddjid: 5,
        qy: 0.0,
        zhcysj: "Jun 26, 2025 10:29:33 AM",
        dgid: 4449,
        dgname: "智慧灯杆位置西",
        sdlevel: 1,
        wdlevel: 1,
        zslevel: 1,
        pm25level: 1,
        pm10level: 3,
        zdlevel: 3,
        qylevel: 1,
      },
      xsp: {
        code: "xsp001",
        name: "显示屏位置西",
        ckzclxid: 23,
        parentid: 1,
        parentname: "测控组成",
        jzzcid: null,
        jzzcname: null,
        azwz: null,
        jd: null,
        wd: null,
        csid: 1,
        zhtxsj: null,
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Oct 9, 2024 5:27:31 PM",
        memo: null,
        id: 4475,
        kzztid: null,
        kzztms: null,
        xskd: null,
        xsgd: null,
        xsscid: null,
        xsscms: null,
        xshdid: null,
        isupdate: null,
        isupdatetz: null,
        ip: "************",
        port: 80,
        kzfsid: null,
        xcbyjmsj: null,
        dqfqid: null,
        direction: null,
        content: null,
        lastpublishtime: null,
        dgid: 4449,
        dgname: "智慧灯杆位置西",
        fileurl: "group1/M00/12/CF/wKgB6mhcsKiAdqlQAAAFk15FqYw734.jpg",
      },
      sxj: {
        code: "sxj001",
        name: "摄像机位置西",
        ckzclxid: 16,
        parentid: 1,
        parentname: "测控组成",
        jzzcid: null,
        jzzcname: null,
        azwz: null,
        jd: null,
        wd: null,
        zhtxsj: null,
        createuserid: "1",
        createusername: "开发管理员",
        createtime: "Feb 17, 2025 1:12:24 PM",
        memo: null,
        id: 4484,
        nwip: null,
        nwport: null,
        nwwebport: null,
        wwip: null,
        wwport: null,
        wwwebport: null,
        username: null,
        password: null,
        gbcode: "41010500001320000702",
        gbtdcode: "41010500001320000712",
        td: 1,
        csid: 1,
        ws: null,
        sbmc: null,
        sfjl: null,
        jlsbip: null,
        jlsbtd: null,
        zcdw: null,
        sddw: null,
        dwnfz: null,
        canloginstate: 2,
        lastlogintime: null,
        protocol: 4,
        nvrid: null,
        indexcode: null,
        jcfxid: null,
        dgid: 4449,
        dgname: "智慧灯杆位置西",
      },
    },
  },
]);
const api_token_url = ref(
  "https://lscykj.5158888.com/api/sys/auth/token/ldjsc001/e714fa28567febc8ff9feec69ea0b3a0"
);
const token = ref("");
const tokenEpireTime = ref("");
const axiosDate = ref({
  endTime: "2025-06-01 23:59:59",
  startTime: "2025-06-01 00:00:00",
});

// 视频对话框状态
const videoDialog = ref({
  title: "视频监控",
  show: false,
  id: "videoDialog",
  data: {},
});
const light_location = ref({
  智慧灯杆位置东: {
    jd: "120.37868033693988",
    wd: "36.20482608050773",
    videourl:
      "https://lscykj.5158888.com/live/cameraid/1000022%240/substream/2.flv?token=1:ual6ILy2MDrvv2yI9Rnydmwj4gDqXN7J",
  },
  智慧灯杆位置西: {
    jd: "120.37720338245066",
    wd: "36.20433974959927",
    videourl:
      "https://lscykj.5158888.com/live/cameraid/1000022%2411/substream/2.flv?token=1:ual6ILy2MDrvv2yI9Rnydmwj4gDqXN7J",
  },
  智慧灯杆位置北: {
    jd: "120.3787888925826",
    wd: "36.20547049763052",
    videourl:
      "https://lscykj.5158888.com/live/cameraid/1000022%242/substream/2.flv?token=1:ual6ILy2MDrvv2yI9Rnydmwj4gDqXN7J",
  },
  智慧灯杆位置南: {
    jd: "120.37816365084426",
    wd: "36.20412387069241",
    videourl:
      "https://lscykj.5158888.com/live/cameraid/1000022%2412/substream/2.flv?token=1:ual6ILy2MDrvv2yI9Rnydmwj4gDqXN7J",
  },
  智慧灯杆位置中: {
    jd: "120.37818928943997",
    wd: "36.2048903983935",
    videourl:
      "https://lscykj.5158888.com/live/cameraid/1000022%241/substream/2.flv?token=1:ual6ILy2MDrvv2yI9Rnydmwj4gDqXN7J",
  },
});
// 视频播放器相关
let flvPlayer = null;

// 路灯点位实体数据源
const lightDataSource = ref(null);

// 存储事件监听器引用
let entityClickListener = null;
let screenSpaceEventHandler = null;

onMounted(() => {
  emitter.emit("showFooterTool", false);

  axios.get(api_token_url.value).then((res) => {
    token.value = res.data.data.token;
    tokenEpireTime.value = res.data.data.expireTime;
    if (Date.now() > tokenEpireTime) {
      ElMessage({
        message: "获取智能化信息组件已过期，请关闭该面板重新打开",
        type: "warning",
      });
    } else {
      getLightData();
    }
  });

  // 添加entity点击事件监听器
  if (window.viewer) {
    // 添加selectedEntityChanged事件监听器
    entityClickListener =
      window.viewer.selectedEntityChanged.addEventListener(handleEntityClick);
    console.log("Entity点击事件监听器已添加:", entityClickListener);

    // 添加直接的点击事件监听器
    if (!screenSpaceEventHandler && window.Cesium) {
      screenSpaceEventHandler = new window.Cesium.ScreenSpaceEventHandler(
        window.viewer.scene.canvas
      );
      screenSpaceEventHandler.setInputAction((click) => {
        console.log("屏幕点击事件触发");
        const pickedObject = window.viewer.scene.pick(click.position);
        console.log("点击对象:", pickedObject);

        if (window.Cesium.defined(pickedObject) && pickedObject.id) {
          const entity = pickedObject.id;
          console.log("点击了entity:", entity);

          // 如果是路灯entity，处理视频播放
          if (entity.id && entity.id.toString().startsWith("light_")) {
            const entityName = entity.name;
            handleLightEntityClick(entityName);
          }
        }
      }, window.Cesium.ScreenSpaceEventType.LEFT_CLICK);

      console.log("屏幕点击事件监听器已添加");
    }

    // 测试事件监听器
    setTimeout(() => {
      console.log("测试viewer状态:", {
        viewer: !!window.viewer,
        selectedEntityChanged: !!window.viewer?.selectedEntityChanged,
        lightDataSource: !!lightDataSource.value,
        entitiesCount: lightDataSource.value?.entities?.values?.length || 0,
        screenSpaceEventHandler: !!screenSpaceEventHandler,
      });
    }, 2000);
  }

  // 在mounted和nextTick之后启动滚动，确保DOM已渲染且表格数据已加载
  nextTick(() => {});
});

onUnmounted(() => {
  emitter.emit("showFooterTool", true);

  // 移除entity点击事件监听器
  if (entityClickListener && window.viewer) {
    entityClickListener();
    entityClickListener = null;
  }

  // 移除屏幕事件监听器
  if (screenSpaceEventHandler) {
    screenSpaceEventHandler.destroy();
    screenSpaceEventHandler = null;
  }

  // 清理路灯点位实体
  clearLightEntities();

  // 清理视频播放器
  if (flvPlayer) {
    flvPlayer.destroy();
    flvPlayer = null;
  }
});

// 初始化路灯点位数据源
const initLightDataSource = () => {
  if (!window.viewer) {
    console.warn("Cesium viewer 未初始化");
    return;
  }
  if (lightDataSource.value) {
    lightDataSource.value = null;
  }
  // 创建路灯点位数据源
  lightDataSource.value = new Cesium.CustomDataSource("lightPoints");
  window.viewer.dataSources.add(lightDataSource.value);
};

// 清理路灯点位实体
const clearLightEntities = () => {
  if (lightDataSource.value && window.viewer) {
    window.viewer.dataSources.remove(lightDataSource.value);
    lightDataSource.value = null;
  }
};

// 创建路灯点位实体
const createLightEntities = () => {
  if (!lightDataSource.value || !ludeng_info.value) {
    return;
  }

  // 清空现有实体
  lightDataSource.value.entities.removeAll();

  // 为每个路灯创建点位实体
  ludeng_info.value.forEach((light, index) => {
    if (light.jd && light.wd) {
      // 根据开关状态确定颜色
      const isOn = light.detail?.ld?.dqkgztid !== 2;
      const color = isOn ? Cesium.Color.YELLOW : Cesium.Color.GRAY;
      // const WGS84lonlat = bd09ToWgs84(light.jd, light.wd);
      var entity2 = {
        id: `light_${light.id || index}`,
        name: light.name || `路灯${index + 1}`,
        clampToGround: true,
        position: Cesium.Cartesian3.fromDegrees(light.jd, light.wd, 50),
        label: {
          font: "600 15px STHeiti",
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.fromCssColorString("rgba(16,17,18,1)"),
          outlineWidth: 4,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0.0, -35),
          text: light.name || `路灯${index + 1}`,
          disableDepthTestDistance: 10000,
          distanceDisplayCondition: null,
        },
        polyline: {
          show: true,
          positions: Cesium.Cartesian3.fromDegreesArrayHeights([
            light.jd,
            light.wd,
            0,
            light.jd,
            light.wd,
            50,
          ]),
          width: 5,
          material: new Cesium.PolylineOutlineMaterialProperty({
            color: Cesium.Color.WHITE,
            outlineWidth: 3,
            outlineColor: Cesium.Color.fromCssColorString("#0066FF"),
          }),
        },
        billboard: {
          // disableDepthTestDistance: Number.POSITIVE_INFINITY,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          image: "/images/钻孔.png",
          height: 24,
          width: 24,
        },
      };
      lightDataSource.value.entities.add(entity2);
    }
  });

  console.log(
    `已创建 ${lightDataSource.value.entities.values.length} 个路灯点位实体`
  );
};

// 监听组件可见性，当组件可见时创建点位实体
watch(
  () => props.visible,
  (newValue) => {
    console.log("watch triggered, visible:", newValue);

    if (
      newValue &&
      // lightDataSource.value &&
      ludeng_info.value &&
      ludeng_info.value.length > 0
    ) {
      // 初始化路灯点位数据源
      initLightDataSource();
      // 组件显示时创建点位实体
      console.log("创建路灯点位实体");
      createLightEntities();

      // 确保事件监听器已添加
      if (window.viewer && !entityClickListener) {
        entityClickListener =
          window.viewer.selectedEntityChanged.addEventListener(
            handleEntityClick
          );
        console.log("添加entity点击事件监听器");
      }

      // 确保屏幕事件监听器已添加
      if (window.viewer && !screenSpaceEventHandler && window.Cesium) {
        screenSpaceEventHandler = new window.Cesium.ScreenSpaceEventHandler(
          window.viewer.scene.canvas
        );
        screenSpaceEventHandler.setInputAction((click) => {
          console.log("屏幕点击事件触发");
          const pickedObject = window.viewer.scene.pick(click.position);
          console.log("点击对象:", pickedObject);

          if (window.Cesium.defined(pickedObject) && pickedObject.id) {
            const entity = pickedObject.id;
            console.log("点击了entity:", entity);

            // 如果是路灯entity，处理视频播放
            if (entity.id && entity.id.toString().startsWith("light_")) {
              const entityName = entity.name;
              handleLightEntityClick(entityName);
            }
          }
        }, window.Cesium.ScreenSpaceEventType.LEFT_CLICK);

        console.log("屏幕点击事件监听器已添加");
      }
    } else if (!newValue) {
      // 组件隐藏时清理点位实体
      console.log("清理路灯点位实体");
      debugger;
      if (lightDataSource.value) {
        lightDataSource.value.entities.removeAll();
      }
    }
  },
  { immediate: true } // 立即执行一次
);

//获取灯杆数据
const getLightData = () => {
  var data = JSON.stringify({ key: null, page: 0, size: 1000 });

  var config = {
    method: "post",
    url: "https://lscykj.5158888.com/api/sys/zhdg/dg/list",
    headers: {
      authorization: "Bearer " + token.value,
      "Content-Type": "application/json",
    },
    data: data,
  };

  axios(config).then(function (response) {
    debugger;

    let result = response.data.data;
    let lightList = [];
    let lightDetailAxiosList = [];
    try {
      result.map((item, index) => {
        let light = {};
        (light.code = item.code), (light.name = item.name);
        light.jzzcid = item.jzzcid;
        light.azwz = item.azwz;
        light.jd = item.jd;
        light.wd = item.wd;
        light.zhtxsj = item.zhtxsj;
        light.createuserid = item.createuserid;
        light.createusername = item.createusername;
        light.createtime = item.createtime;
        light.id = item.id;
        light.detail = {};
        lightList.push(light);

        let axiosRequest = axios({
          method: "post",
          url: "https://lscykj.5158888.com/api/sys/zhdg/dg/info",
          headers: {
            authorization: "Bearer " + token.value,
            "Content-Type": "application/json",
          },
          data: JSON.stringify({
            id: item.id,
          }),
        });
        lightDetailAxiosList.push(axiosRequest);
      });

      // 不要完全替换ludeng_info，而是根据获取到的数据更新现有的灯杆信息
      // 首先确保ludeng_info有足够的元素
      while (ludeng_info.value.length < lightList.length) {
        ludeng_info.value.push({
          code: "",
          name: "",
          id: null,
          detail: {},
        });
      }

      // 更新基础信息
      lightList.forEach((light, index) => {
        if (ludeng_info.value[index]) {
          ludeng_info.value[index].code = light.code;
          ludeng_info.value[index].name = light.name;
          if (light_location.value[light.name]) {
            ludeng_info.value[index].jd = light_location.value[light.name].jd;
            ludeng_info.value[index].wd = light_location.value[light.name].wd;
          }
          ludeng_info.value[index].id = light.id;

          ludeng_info.value[index].azwz = light.azwz;
          ludeng_info.value[index].zhtxsj = light.zhtxsj;
        }
      });

      console.log("发送详情请求数量:", lightDetailAxiosList.length);
      console.log("当前ludeng_info长度:", ludeng_info.value.length);

      axios
        .all(lightDetailAxiosList)
        .then(
          axios.spread((...responses) => {
            console.log("收到详情响应数量:", responses.length);
            debugger;
            // 处理所有详情请求的响应
            responses.forEach((response, index) => {
              console.log(`处理第${index}个响应:`, response.data);

              if (
                response.data &&
                response.data.code === 0 &&
                response.data.data
              ) {
                const ludengDetailInfo = response.data.data;
                console.log(`第${index}个灯杆详情数据:`, ludengDetailInfo);

                // 更新对应的灯杆详情数据
                if (ludeng_info.value[index]) {
                  // 直接更新detail对象，保持原有结构
                  if (!ludeng_info.value[index].detail) {
                    ludeng_info.value[index].detail = {};
                  }
                  ludeng_info.value[index].detail = response.data.data;
                  // // 更新网关信息
                  // if (ludengDetailInfo.wg) {
                  //   ludeng_info.value[index].detail.wg = ludengDetailInfo.wg;
                  //   console.log(`更新第${index}个灯杆的网关信息`);
                  // }

                  // // 更新路灯信息
                  // if (ludengDetailInfo.ld) {
                  //   ludeng_info.value[index].detail.ld = ludengDetailInfo.ld;
                  //   console.log(
                  //     `更新第${index}个灯杆的路灯信息:`,
                  //     ludengDetailInfo.ld
                  //   );
                  // }

                  // // 更新气象站信息
                  // if (ludengDetailInfo.qxz) {
                  //   ludeng_info.value[index].detail.qxz = ludengDetailInfo.qxz;
                  //   console.log(
                  //     `更新第${index}个灯杆的气象站信息:`,
                  //     ludengDetailInfo.qxz
                  //   );
                  // }

                  // // 更新显示屏信息
                  // if (ludengDetailInfo.xsp) {
                  //   ludeng_info.value[index].detail.xsp = ludengDetailInfo.xsp;
                  //   console.log(`更新第${index}个灯杆的显示屏信息`);
                  // }

                  // // 更新摄像机信息
                  // if (ludengDetailInfo.sxj) {
                  //   ludeng_info.value[index].detail.sxj = ludengDetailInfo.sxj;
                  //   console.log(`更新第${index}个灯杆的摄像机信息`);
                  // }

                  // // 更新基础信息
                  // ludeng_info.value[index].detail.name = ludengDetailInfo.name;
                  // ludeng_info.value[index].detail.code = ludengDetailInfo.code;
                  // ludeng_info.value[index].detail.id = ludengDetailInfo.id;
                  // ludeng_info.value[index].detail.zhtxsj =
                  //   ludengDetailInfo.zhtxsj;
                  // ludeng_info.value[index].detail.jd = ludengDetailInfo.jd;
                  // ludeng_info.value[index].detail.wd = ludengDetailInfo.wd;
                  // ludeng_info.value[index].detail.azwz = ludengDetailInfo.azwz;

                  // console.log(
                  //   `第${index}个灯杆更新后的detail:`,
                  //   ludeng_info.value[index].detail
                  // );
                } else {
                  console.warn(`ludeng_info[${index}] 不存在`);
                }
              } else {
                console.error(`第${index}个响应数据格式错误:`, response.data);
              }
            });

            console.log("灯杆详情数据处理完成:", ludeng_info.value);

            // 强制触发响应式更新
            ludeng_info.value = [...ludeng_info.value];

            // 如果组件可见且数据源已初始化，创建路灯点位实体
            if (props.visible && lightDataSource.value) {
              createLightEntities();
            }
          })
        )
        .catch((error) => {
          console.error("获取灯杆详情数据失败:", error);
          ElMessage({
            message: "获取灯杆详情数据失败",
            type: "error",
          });
        });
    } catch (e) {
      alert(e);
    }
  });
};
// 检测是否为flv格式的URL
function isFlvUrl(url) {
  return /\.flv($|\?)/i.test(url);
}

// 处理摄像头表格行点击事件
const handleCameraRowClick = (row) => {
  if (!row.videoUrl) {
    console.warn("该摄像头没有视频流地址");
    return;
  }

  // 设置视频对话框信息
  videoDialog.value.title = `视频监控 - ${row.location}`;
  videoDialog.value.show = true;
  videoDialog.value.data = { url: row.videoUrl };

  // 等待DOM更新后播放视频
  nextTick(() => {
    playVideo(row.videoUrl);
  });
};

// 播放视频
const playVideo = (url) => {
  const video = document.getElementById("cameraVideoPlayer");
  if (!video) {
    console.error("找不到视频播放器元素");
    return;
  }

  // 销毁旧的播放器
  if (flvPlayer) {
    flvPlayer.destroy();
    flvPlayer = null;
  }

  // 检查是否需要flv.js播放器
  if (isFlvUrl(url) && window.flvjs && window.flvjs.isSupported()) {
    try {
      flvPlayer = window.flvjs.createPlayer({
        type: "flv",
        url: url,
        isLive: true,
      });
      flvPlayer.attachMediaElement(video);
      flvPlayer.load();
      flvPlayer.play().catch((error) => {
        console.error("视频播放失败:", error);
      });
    } catch (error) {
      console.error("创建flv播放器失败:", error);
      // 降级到普通video标签播放
      video.src = url;
      video.load();
      video.play().catch((err) => {
        console.error("视频播放失败:", err);
      });
    }
  } else {
    // 使用普通video标签播放
    video.src = url;
    video.load();
    video.play().catch((error) => {
      console.error("视频播放失败:", error);
    });
  }
};

// 处理entity点击事件
const handleEntityClick = () => {
  console.log("Entity点击事件触发");

  if (!window.viewer) {
    console.error("Cesium viewer未初始化");
    return;
  }

  const selectedEntity = window.viewer.selectedEntity;
  if (!selectedEntity) {
    console.log("没有选中的entity");
    return;
  }

  console.log("选中的entity:", selectedEntity);
  console.log("entity ID:", selectedEntity.id);
  console.log("entity name:", selectedEntity.name);

  // 检查是否是路灯entity
  if (selectedEntity.id && selectedEntity.id.toString().startsWith("light_")) {
    const entityName = selectedEntity.name;
    console.log("点击了路灯entity:", entityName);
    console.log("可用的light_location:", Object.keys(light_location.value));

    // 从light_location中获取对应的视频URL
    if (
      light_location.value[entityName] &&
      light_location.value[entityName].videourl
    ) {
      const videoUrl = light_location.value[entityName].videourl;
      console.log("找到视频URL:", videoUrl);

      // 设置视频对话框信息
      videoDialog.value.title = `视频监控 - ${entityName}`;
      videoDialog.value.show = true;
      videoDialog.value.data = { url: videoUrl };
      console.log("视频对话框状态:", videoDialog.value);

      // 等待DOM更新后播放视频
      nextTick(() => {
        console.log("DOM更新完成，开始播放视频");
        playVideo(videoUrl);
      });
    } else {
      console.warn(`未找到${entityName}对应的视频URL`);
      ElMessage.warning(`${entityName}暂无视频监控`);
    }
  } else {
    console.log("点击的不是路灯entity");
  }
};

// 处理路灯entity点击事件（从屏幕点击事件调用）
const handleLightEntityClick = (entityName) => {
  console.log("处理路灯entity点击:", entityName);
  console.log("可用的light_location:", Object.keys(light_location.value));

  // 从light_location中获取对应的视频URL
  if (
    light_location.value[entityName] &&
    light_location.value[entityName].videourl
  ) {
    const videoUrl = light_location.value[entityName].videourl;
    console.log("找到视频URL:", videoUrl);

    // 设置视频对话框信息
    videoDialog.value.title = `视频监控 - ${entityName}`;
    videoDialog.value.show = true;
    videoDialog.value.data = { url: videoUrl };
    console.log("视频对话框状态:", videoDialog.value);

    // 等待DOM更新后播放视频
    nextTick(() => {
      console.log("DOM更新完成，开始播放视频");
      playVideo(videoUrl);
    });
  } else {
    console.warn(`未找到${entityName}对应的视频URL`);
    ElMessage.warning(`${entityName}暂无视频监控`);
  }
};

// 处理视频对话框关闭
const handleVideoDialogClose = () => {
  videoDialog.value.show = false;

  // 停止视频播放
  if (flvPlayer) {
    flvPlayer.destroy();
    flvPlayer = null;
  }

  const video = document.getElementById("cameraVideoPlayer");
  if (video) {
    video.pause();
    video.src = "";
  }

  // 清除选中的entity
  if (window.viewer) {
    window.viewer.selectedEntity = undefined;
  }
};

const getNowFormatDate = () => {
  let date = new Date(),
    year = date.getFullYear(),
    month = date.getMonth() + 1,
    strDate = date.getDate();
  if (month < 10) month = `0${month}`;
  if (strDate < 10) strDate = `0${strDate}`;
  return `${year}-${month}-${strDate}`;
};

// BD-09(百度坐标系)转WGS84坐标系
function bd09ToWgs84(bdLng, bdLat) {
  let gcj = bd09ToGcj02(bdLng, bdLat);
  let wgs84 = gcj02ToWgs84(gcj.lng, gcj.lat);
  return wgs84;
}

// BD-09转GCJ-02
function bd09ToGcj02(bdLng, bdLat) {
  let x = bdLng - 0.0065;
  let y = bdLat - 0.006;
  let z =
    Math.sqrt(x * x + y * y) -
    0.00002 * Math.sin((y * Math.PI * 3000.0) / 180.0);
  let theta =
    Math.atan2(y, x) - 0.000003 * Math.cos((x * Math.PI * 3000.0) / 180.0);
  let gcjLng = z * Math.cos(theta);
  let gcjLat = z * Math.sin(theta);
  return { lng: gcjLng, lat: gcjLat };
}

// GCJ-02(火星坐标系)转WGS84坐标系
function gcj02ToWgs84(lng, lat) {
  if (outOfChina(lng, lat)) {
    return { lng: lng, lat: lat };
  }

  // 计算偏移量
  let d = delta(lng, lat);
  let mgLng = lng - d.lng;
  let mgLat = lat - d.lat;

  return { lng: mgLng, lat: mgLat };
}

// 判断坐标是否在中国范围内
function outOfChina(lng, lat) {
  return lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271;
}

// 计算偏移量
function delta(lng, lat) {
  let a = 6378245.0; // 地球长半轴
  let ee = 0.00669342162296594323; // 扁率

  let dLat = transformLat(lng - 105.0, lat - 35.0);
  let dLng = transformLng(lng - 105.0, lat - 35.0);

  let radLat = (lat / 180.0) * Math.PI;
  let magic = Math.sin(radLat);
  magic = 1 - ee * magic * magic;
  let sqrtMagic = Math.sqrt(magic);

  dLat = (dLat * 180.0) / (((a * (1 - ee)) / (magic * sqrtMagic)) * Math.PI);
  dLng = (dLng * 180.0) / ((a / sqrtMagic) * Math.cos(radLat) * Math.PI);

  return { lng: dLng, lat: dLat };
}

// 纬度转换函数
function transformLat(x, y) {
  let ret =
    -100.0 +
    2.0 * x +
    3.0 * y +
    0.2 * y * y +
    0.1 * x * y +
    0.2 * Math.sqrt(Math.abs(x));
  ret +=
    ((20.0 * Math.sin(6.0 * x * Math.PI) + 20.0 * Math.sin(2.0 * x * Math.PI)) *
      2.0) /
    3.0;
  ret +=
    ((20.0 * Math.sin(y * Math.PI) + 40.0 * Math.sin((y / 3.0) * Math.PI)) *
      2.0) /
    3.0;
  ret +=
    ((160.0 * Math.sin((y / 12.0) * Math.PI) +
      320 * Math.sin((y * Math.PI) / 30.0)) *
      2.0) /
    3.0;
  return ret;
}

// 经度转换函数
function transformLng(x, y) {
  let ret =
    300.0 +
    x +
    2.0 * y +
    0.1 * x * x +
    0.1 * x * y +
    0.1 * Math.sqrt(Math.abs(x));
  ret +=
    ((20.0 * Math.sin(6.0 * x * Math.PI) + 20.0 * Math.sin(2.0 * x * Math.PI)) *
      2.0) /
    3.0;
  ret +=
    ((20.0 * Math.sin(x * Math.PI) + 40.0 * Math.sin((x / 3.0) * Math.PI)) *
      2.0) /
    3.0;
  ret +=
    ((150.0 * Math.sin((x / 12.0) * Math.PI) +
      300.0 * Math.sin((x / 30.0) * Math.PI)) *
      2.0) /
    3.0;
  return ret;
}

// WGS84转CGCS2000（由于两者非常接近，这里做近似处理）
function wgs84ToCgcs2000(wgsLng, wgsLat, wgsHeight = 0) {
  // CGCS2000与WGS84的差异极小，通常可以忽略不计
  // 对于大多数应用场景，可以直接使用WGS84坐标作为CGCS2000坐标
  // 如果需要更精确的转换，可以使用七参数转换法

  // 这里返回相同坐标作为近似
  return {
    lng: wgsLng,
    lat: wgsLat,
    height: wgsHeight,
  };
}

// BD-09直接转换为CGCS2000
function bd09ToCgcs2000(bdLng, bdLat, bdHeight = 0) {
  let wgs84 = bd09ToWgs84(bdLng, bdLat);
  let cgcs2000 = wgs84ToCgcs2000(wgs84.lng, wgs84.lat, bdHeight);
  return cgcs2000;
}
</script>
  
  <script>
import SuWindow from "@/components/su-window.vue";
import axios from "axios";

export default {
  components: {
    SuWindow,
  },
};
</script>
  
  <style scoped>
.dashboard {
  /* background: #0a1437; */
  color: #fff;
  min-height: 100vh;
  height: 100%;
  width: 100%;
  font-family: "Microsoft YaHei", Arial, sans-serif;
  z-index: 11;
  overflow: hidden;
  /* position: fixed; */
  left: 0;
  top: 0;
}
.dashboard-header {
  text-align: center;
  padding: 20px 0 10px 0;
  background: linear-gradient(90deg, #0a1437 60%, #1e2a4a 100%);
  position: relative;
  z-index: 11;
}
.dashboard-header h1 {
  font-size: 2.2rem;
  letter-spacing: 0.2em;
  color: #3deaff;
  margin: 0;
}
.nav {
  position: absolute;
  left: 30px;
  top: 20px;
  z-index: 11;
}
.nav button {
  background: #1e2a4a;
  color: #3deaff;
  border: none;
  margin-right: 10px;
  padding: 8px 18px;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
}
.dashboard-main {
  display: flex;
  justify-content: space-between;
  padding: 0px 2vw;
  z-index: 11;
}
.dashboard-left,
.dashboard-right {
  width: 27%;
  display: flex;
  flex-direction: column;
  gap: 18px;
  z-index: 11;
}
.dashboard-center {
  /* flex: 1; */
  position: fixed;
  bottom: 120px;
  display: flex;
  left: 30%;
  /* align-items: center; */
  /* justify-content: center; */
  z-index: 11;
}
.panel {
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    to bottom,
    #0a1437 0%,
    #1e2a4a 60%,
    #1e2a4ad9 100%
  );
  border-radius: 12px;
  padding: 10px 8px;
  margin-bottom: 5px;
  box-shadow: 0 2px 8px #0006;
  min-height: 350px;
  display: flex;
  flex-direction: column;
}
.panel_center {
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    to bottom,
    #183b61 0%,
    #294b77 60%,
    #0c1f3ed9 100%
  );
  border-radius: 12px;
  padding: 10px 8px;
  margin-bottom: 5px;
  box-shadow: 0 2px 8px #0006;
  /* min-height: 250px; */
  display: flex;
  flex-direction: column;
}
.panel_center h2 {
  font-size: 1rem;
  color: #3deaff;
  /* margin-bottom: 1px; */
  position: absolute;
  top: -13px;
  left: 50px;
}
.panel_small {
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    to bottom,
    #0a1437 0%,
    #1e2a4a 60%,
    #1e2a4ad9 100%
  );
  border-radius: 12px;
  padding: 25px 25px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px #0006;
  min-height: 251px;
}
.panel::before {
  content: "";
  position: absolute;
  left: -30%;
  top: -30%;
  width: 160%;
  height: 160%;
  background: linear-gradient(
      120deg,
      rgba(61, 234, 255, 0.15) 0%,
      rgba(61, 234, 255, 0.05) 100%
    ),
    repeating-linear-gradient(
      60deg,
      rgba(61, 234, 255, 0.08) 0 2px,
      transparent 2px 10px
    );
  filter: blur(8px);
  z-index: 0;
  animation: sci-fi-move 6s linear infinite;
  pointer-events: none;
}
@keyframes sci-fi-move {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  100% {
    transform: translate(20px, 20px) rotate(2deg);
  }
}
.panel > * {
  position: relative;
  z-index: 1;
}
.panel h2 {
  font-size: 1rem;
  color: #3deaff;
  /* margin-bottom: 1px; */
  position: relative;
  top: -20px;
}
.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.highlight {
  font-size: 20px;
  color: #3deaff;
  font-weight: bold;
  margin: 0 4px;
  text-shadow: 0 0 25px #00d8ff;
  font-family: "yjsz";
}
.highlight-red {
  font-size: 20px;
  color: #ff3d43;
  font-weight: bold;
  margin: 0 4px;
  text-shadow: 0 0 25px #d34b51;
  font-family: "yjsz";
}
.highlight-yellow {
  font-size: 20px;
  color: #fafa00;
  font-weight: bold;
  margin: 0 4px;
  text-shadow: 0 0 25px #fafa00;
  font-family: "yjsz";
}
.highlight-green {
  font-size: 20px;
  color: #01f536;
  font-weight: bold;
  margin: 0 4px;
  text-shadow: 0 0 25px #00f825;
  font-family: "yjsz";
}
.echart_dian {
  width: 100%;
  height: 240px;
  margin-top: 10px;
}
.echart {
  width: 100%;
  height: 120px;
  margin-top: 10px;
}
.chart-container {
  flex: 1;
  overflow: hidden;
}
.el-table {
  height: 100%;
  /* 覆盖 Element UI 表格默认背景 */
  background-color: transparent !important;
}

/* 覆盖表格body的背景 */
.el-table__body {
  background-color: transparent !important;
}

/* 覆盖普通行和斑马纹行的背景 */
.el-table tr,
.el-table td,
.el-table th {
  background-color: transparent !important;
}

/* 如果使用了stripe属性，也覆盖斑马纹行的背景 */
.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: transparent !important;
}

.center-image {
  width: 48vw;
  height: 38vw;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #222a3a;
  border-radius: 18px;
  box-shadow: 0 2px 12px #0008;
}
.image-placeholder {
  width: 90%;
  height: 90%;
  background: #444c5c;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  border-radius: 12px;
  border: 2px dashed #3deaff;
}
.video-thumbnails {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}
.video-thumb {
  width: 90px;
  height: 60px;
  background: #222a3a;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3deaff;
  font-size: 0.9rem;
}
.visual_box {
  height: 33%;
  max-width: 583px;
}
.visual_box_center {
  height: 30%;
  max-width: 883px;
}
.visual_box_small {
  height: 25%;
}
.visual_box .zuo {
  width: 58px;
  height: 14px;
  background-image: url(data:image/png;base64,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);
}
.visual_box .you {
}
.visual_box .visual_title {
  position: relative;
  display: flex;
  height: 35px;
}
.visual_box .visual_title span {
  color: #fff;
  font-size: 18px;
  line-height: 35px;
}
.visual_box .visual_title img {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
}
.panel-border-svg {
  position: absolute;
  left: 0;
  top: -10px;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}
.panel-border-svg_small {
  position: absolute;
  left: 0;
  top: -10px;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}
.panel > *:not(.panel-border-svg) {
  position: relative;
  z-index: 3;
}
.elevatorRow {
  justify: center;
  align: middle;
}
.qxDiv {
  width: 90%;
  height: 96%;
  background: rgba(0, 78, 255, 0.2784313725);
  display: flex;
  align-items: center;
  justify-content: space-around;
  color: #fff;
}
.ldInfoValue {
  width: 100%;
  height: 50%;
  text-align: center;
  line-height: 22px;
  color: rgb(0, 234, 255);
  font-weight: 600;
  font-size: 18px;
}
.ldInfoType {
  width: 100%;
  height: 50%;
  text-align: center;
  line-height: 22px;
  font-size: 14px;
}
.ldInfoImg {
  width: 30px;
  height: 30px;
}
.el-col-12 {
  margin-top: 20px;
  padding-left: 5px;
}
.yelloCircle {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background: yellow;
  margin-top: 7px;
  margin-right: 10px;
}
.infoName {
}
.infoNameCenter {
  margin-top: 10px;
  padding-left: 5px;
}
.infoValue {
  line-height: 28px;
  padding-left: 20px;
  font-size: 18px;
  font-weight: 600;
}
.infoValueRed {
  font-size: 20px;
  color: #ff3d43;
  font-weight: bold;
  text-shadow: 0 0 25px #d34b51;
  font-family: "yjsz";
}
.infoValueGreen {
  font-size: 20px;
  color: #00ff1a;
  font-weight: bold;
  text-shadow: 0 0 25px #00ff1a;
  font-family: "yjsz";
}
.infoValueCenter {
  margin-top: 10px;
  padding-left: 5px;
  font-size: 18px;
  font-weight: 600;
}

/* 视频对话框样式 */
.video-dialog {
  z-index: 9999;
}

.video-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
}

.video-player {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
</style>
  <style>
html,
body {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden !important;
}
/* 不能加scope属性，否则不生效 */
@keyframes myfirst {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-359deg);
  }
}
/* 关键修改：使用 translateY 负值实现无缝循环 */
.el-table__body-wrapper {
  animation: scroll 15s linear infinite;
  will-change: transform; /* 优化动画性能 */
}
</style>
  