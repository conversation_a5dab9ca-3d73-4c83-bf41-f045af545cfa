// 事件管理
import EventManager from "../../js/common/eventManager/EventManager.js";
import { callWithAsyncErrorHandling, onBeforeUnmount } from "vue";
import MapLayerUtil from "../common/class/MapLayerUtil.js";
import { resourceURL, webstationConfig, host } from "@/config/resoureURL.js";
import keyboardControl from "@/utils/keyboardControl";
import LayerController from "../common/class/LayerController.js";
import iserverMapLayer from "../common/class/iserverMapLayer.js";
import SceneController from "../common/class/sceneController.js";
import axios from "axios";
import { getToken } from "@/js/common/common.js";
import store from '@/store'

// 初始化地球
async function initViewer(props, callback) {
  // Cesium.Ellipsoid.WGS84 = Object.freeze(
  //   new Cesium.Ellipsoid(6378137.0, 6378137.0, 6356752.3142451793),
  // );
  axios
    .get(
      store.state.iportalHostUrl +
      "/iportal/web/config/userprofile.json?scope=nickName&token=" +
      getToken()
    )
    .then(function (res) {

    }).catch(e => {
      ElMessage.error("登录信息已过期 请重新登陆");
      window.location.href = window.location.href + 'login'
    });

  await axios.get("/data/config/sceneConfig.json").then(res => {
    if (res.data) {
      store.commit('updateS3mMaxVisibleAltitude', res.data.maxVisibleAltitude)
    }
  })
  // Cesium.Ellipsoid.WGS84 = Object.freeze(
  //   new Cesium.Ellipsoid(6378137.0, 6378137.0, 6356752.3142451793),
  // );
  // 初始化viewer
  if (window.viewer) {
    window.viewer = null;
  }
  // debugger
  let obj = [6378137.0, 6378137.0, 6356752.3142451793];
  Cesium.Ellipsoid.WGS84 = Object.freeze(
    new Cesium.Ellipsoid(obj[0], obj[1], obj[2])
  );
  let viewer = new Cesium.Viewer("cesiumContainer", {
    selectionIndicator: false,
    timeline: false,
    baseLayerPicker: false,
    // shadows: true,
    infoBox: false,
    // geocoder: true,  //查询
    // skyBox: false, // 关闭天空盒会一同关闭太阳，场景会变暗
    navigation: false,
    // contextOptions: {
    //   msaaLevel: 8,
    //   requestWebgl2: true
    // }
  });
  window.viewer = viewer;

  Cesium.MemoryManager.setMaxMemory(6000);
  // viewer.scene.globe.depthTestAgainstTerrain = false;
  // viewer.scene.undergroundMode = true;
  // viewer.terrainProvider.isCreateSkirt = false; // 去掉裙边
  // viewer.scene.globe.isCreateSkirt = false; // 去掉裙边
  // viewer.scene.globe.baseColor = Cesium.Color.BLACK.withAlpha(0.1);
  // viewer.scene.screenSpaceCameraController.minimumZoomDistance = -200;
  // //调整默认亮度
  // window.viewer.scene.colorCorrection.show = true
  // window.viewer.scene.colorCorrection.brightness = 1.1
  let startTime = new Date();
  startTime.setHours(Number(12));
  // viewer.clock.currentTime = Cesium.JulianDate.fromDate(startTime);

  LayerController.initViewer(viewer);
  iserverMapLayer.init(viewer); //全局仅执行一次。
  SceneController.init(viewer);
  var widget = viewer.cesiumWidget;


  //天地图影像
  // var img = new Cesium.WebMapTileServiceImageryProvider({
  //   url: store.state.layer_url.tdt_img_url,
  //   layer: "SDRasterPubMap",
  //   style: 'default',
  //   format: 'image/jpeg',
  //   tileMatrixSetID: 'sdrasterpubmap',
  //   tileMatrixLabels: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15",
  //     "16", "17", "18", "19"
  //   ],
  //   tilingScheme: new Cesium.GeographicTilingScheme({
  //     rectangle: Cesium.Rectangle.fromDegrees(-180.0, -90.0, 180.0, 90.0)
  //   }),
  //   maximumLevel: 18
  // });
  var arcgisImageLayer = new Cesium.CGCS2000MapServerImageryProvider(
    {
      url: store.state.tomcatHostUrl +
        "/TileServer/arcgis/rest/services/YX2023/MapServer",
    }
  );
  // var arcgisImageLayer = new Cesium.SuperMapImageryProvider(
  //   {
  //     url: 'http://localhost:8090/iserver/services/map-arcgis-YX2023/rest/maps/YX2023'
  //   }
  // );
  // window.img = img
  window.img = arcgisImageLayer
  // store.state.layers.imageLayer = viewer.imageryLayers.addImageryProvider(window.img);
  window.tileImageLayer = viewer.imageryLayers.addImageryProvider(window.img);
  // window.tileImageLayer = viewer.imageryLayers.addImageryProvider(window.img);

  store.state.layers.imageLayer = window.tileImageLayer

  MapLayerUtil.initViewer(viewer);
  // 加载行政区边界
  var queryPara = {
    getFeatureMode: 'SQL',
    datasetNames: ["区划范围:用地范围"],
    queryParameter: {
      attributeFilter: "SMID > 0"
    }
  }

  var quhuaColor = Cesium.Color.fromCssColorString("#66AEEC");
  MapLayerUtil.loadIserverWfs(
    "data-JiaoZhouWanKeChuangXinQuV2",
    queryPara,
    quhuaColor,
    null,
    null
  )
  try {
    let promises = [];
    // 3cm倾斜
    let promise = viewer.scene.open(
      store.state.layer_url.s3mLayer,
      undefined,
      {
        autoSetView: false,
      },
    );
    promise.then(function (layers) {
      for (let j = 0; j < layers.length; j++) {
        store.state.scene3cmList.push(layers[j].name);

      }
    })

    //开启 tick
    viewer.clock.shouldAnimate = true;
    //每次旋转的弧度 越小越慢
    var angle = Cesium.Math.toRadians(Math.PI * 0.5);
    // 旋转次数 用来控制停止
    var rotate_num = 0;

    function onTickCallback() {
      viewer.scene.camera.rotateLeft(angle);
      //以下用来控制 停止
      rotate_num++;
      // 110 次旋转一周
      if (rotate_num === 110) {
        //结束旋转
        viewer.clock.onTick.removeEventListener(onTickCallback);
        //可以再接定位动画
        viewer.camera.flyTo({
          destination: Cesium.Cartesian3.clone({
            x: -2606224.8626752375,
            y: 4446918.56541049,
            z: 3745611.7876181784
          }),
          orientation: {
            direction: Cesium.Cartesian3.clone({
              x: 0.49332025707603744,
              y: -0.7805688308143381,
              z: 0.3838585993820765
            }),
            up: Cesium.Cartesian3.clone({
              x: -0.17785728332986708,
              y: 0.3414547703666864,
              z: 0.9229168037046204
            }),
            heading: 4.806359027065429,
            pitch: -0.35298601132721585,
            roll: 6.283185307179586,
          },
          duration: 4,
        });
      }
    }
    // 利用时钟进行监听
    viewer.clock.onTick.addEventListener(onTickCallback);

    viewer.scene.globe.depthTestAgainstTerrain = false;
    viewer.scene.screenSpaceCameraController.maximumZoomDistance = 22000;

  } catch (ex) {

  }

  var buildhandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  buildhandler.setInputAction(function (e) {
    // onMouseClick(viewer, e);
    onMouseClick(e);
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);


  function onMouseClick(e) {
    //S3M类型的图层可用
    var layers = viewer.scene.layers;
    var layerCount = layers._layers.length;
    var position = viewer.scene.pickPosition(e.position);
    var pick = viewer.scene.pick(e.position);
    //stream有pick.primitive._type属性
    //剖切后的地质体查询有 pick.primitive._name 属性
    store.commit("updatepropertyDIVLoadingState", true);
    debugger
    if (
      Cesium.defined(pick) &&
      pick.id &&
      !pick.primitive._type
      &&
      pick.primitive._name
    ) {
      //非流式数据的查询
      if (pick.primitive._name.indexOf("solidModels") < 0) {
        queryS3MLayerAttr(position);
      } else {
        queryStreamLayerAttr(pick);
      }
    } else if (
      Cesium.defined(pick) &&
      pick.id &&
      pick.primitive._type == "Instanced_Object" &&
      store.getters.getGeoglogySearchState
    ) {
      //以流方式加载的,如地质体
      let pickedPrimitiveUrl = pick.primitive._ownerGroup.url;
      store.state.models.picked_dzt_url = pickedPrimitiveUrl;
      //数据源名称
      let pickedPrimitiveDatasourceName = pickedPrimitiveUrl
        .split("/datasets")[0]
        .split("datasources/")[1];
      store.state.models.picked_dzt_datasourcename =
        pickedPrimitiveDatasourceName;
      //数据集名称
      let pickedPrimitiveDatasetName = pickedPrimitiveUrl
        .split("/features")[0]
        .split("datasets/")[1];
      store.state.models.picked_dzt_datasetname = pickedPrimitiveDatasetName;
      queryStreamLayerAttr(pick);
      debugger;
    }
    //地质体剖切后的点击查询
    // else if (Cesium.defined(pick) && (pick.id) && pick.primitive._name && pick.primitive._name.indexOf("solidModels") >= 0) {

    // }
  }

  function queryS3MLayerAttr(position) {
    var layers = viewer.scene.layers;
    var layerCount = layers._layers.length;
    if (!position) {
      position = Cesium.Cartesian3.fromDegrees(0, 0, 0);
    }
    let scenePosition = position; // 气泡相关 2/4
    // 从笛卡尔坐标获取经纬度
    var cartographic = Cesium.Cartographic.fromCartesian(position);
    var longitude = Cesium.Math.toDegrees(cartographic.longitude);
    var latitude = Cesium.Math.toDegrees(cartographic.latitude);
    var height = cartographic.height;

    let scratchNromal = new Cesium.Cartesian3();

    var IDs = [];
    var layerName = null;
    var idList = [];
    var normal = null;
    var chooseNormal = null;
    for (var i = 0; i < layerCount; i++) {
      var layer = layers.findByIndex(i);
      console.log(i);
      var id;
      if (layer.getSelection) {
        id = layer.getSelection();
      }

      if (id > 0 && layer._name == "oceanlayer") break;
      if (id > 0) {
        console.log("图层名称:" + layer._name + " SMID:" + id);
        debugger;
        IDs.push(parseInt(id));
        layerName = layer._name;
        if (layerName.indexOf('FINAL_CYKJ_') > -1) {
          Window.udbname = "345地下合并种类";
          Window.searchUrl = store.state.layer_url.BIMFINALDataLayer;
        }
        else if (layerName.indexOf('CYKJ_') > -1) {
          Window.udbname = "TABLE";
          Window.searchUrl = store.state.layer_url.BIMDataLayer;
        }

        //手工精细模型
        if (
          layerName.indexOf("dx2") > -1
        ) {
          break;
        }
        searchS3MFeatureInfo(layerName, IDs);
        break;
      }
    }
  }

  //地质体查询
  function queryStreamLayerAttr(pick) {
    //直接点击查询地质体
    if (
      pick &&
      pick.primitive &&
      pick.primitive._ownerGroup &&
      pick.primitive._ownerGroup.url
    ) {
      let streamUrl = pick.primitive._ownerGroup.url;
      let attrUrl =
        streamUrl.replace(".stream", ".json") + "?hasGeometry=false";
      axios.get(attrUrl).then(function (res) {
        document.getElementById("bubbleTableBody").innerHTML = "";
        var html = "";
        debugger;
        let dcmc =
          res.data.fieldValues[res.data.fieldNames.indexOf("地层名称")] || res.data.fieldValues[res.data.fieldNames.indexOf("地质名称")];
        store.commit("updateBubbleLayerName", dcmc);
        var propertyDataArr = []
        if (res.data && res.data.fieldNames && res.data.fieldValues) {
          for (let i = 0; i < res.data.fieldNames.length; i++) {
            if (
              res.data.fieldNames[i].indexOf("SMID") > -1 ||
              res.data.fieldNames[i].indexOf("SMLENGTH") > -1 ||
              res.data.fieldNames[i].indexOf("SMTOPOERROR") > -1 ||
              res.data.fieldNames[i].indexOf("SMUSERID") > -1 ||
              res.data.fieldNames[i].indexOf("SMSDRIW") > -1 ||
              res.data.fieldNames[i].indexOf("SMSDRIN") > -1 ||
              res.data.fieldNames[i].indexOf("SMSDRIE") > -1 ||
              res.data.fieldNames[i].indexOf("SMSDRIS") > -1 ||
              res.data.fieldNames[i].indexOf("SMUSERID") > -1 ||
              res.data.fieldNames[i].indexOf("SMLIBTILEID") > -1 ||
              res.data.fieldNames[i].indexOf("SMGEOMETRYSIZE") > -1 ||
              res.data.fieldNames[i].indexOf("SMGEOPOSITION") > -1 ||
              res.data.fieldNames[i].indexOf("SMMAXZ") > -1 ||
              res.data.fieldNames[i].indexOf("SMMINZ") > -1 ||
              res.data.fieldNames[i].indexOf("RSMTRIANGLECOUNT") > -1 ||
              res.data.fieldNames[i].indexOf("RSMVERTEXCOUNT") > -1 ||
              res.data.fieldNames[i].indexOf("SMINDEXKEY") > -1
            ) {
              continue;
            } else {
              if (res.data.fieldValues[i] == "\\") {
                continue;
              } else {
                // html +=
                //   "<tr><td style='padding-bottom: 10px;'>" +
                //   res.data.fieldNames[i] +
                //   "</td><td style='padding-bottom: 10px;'>" +
                //   res.data.fieldValues[i] +
                //   "</td></tr>";
                propertyDataArr.push(
                  {
                    caption: res.data.fieldNames[i],
                    value: res.data.fieldValues[i]
                  },
                )
              }
            }
          }
          store.commit('updatepropertyWindowShowState', true)
          store.commit('updateClickPropertyData', propertyDataArr)
          // document.getElementById("bubbleTableBody").innerHTML = html;
          // document.getElementById("bubble").style.display = "block";

          //绑定气泡关闭事件
          // document.getElementById("bubble").onclick = () => {
          //   document.getElementById("bubble").style.display = "none";
          // }
        }
      });
    }
    //剖切分析后的地质体查询
    else if (
      pick &&
      pick.primitive &&
      pick.primitive._name &&
      pick.primitive._name == "solidModelsProfile3"
    ) {
      let pickedID = parseInt(pick.primitive._selectedProfileIDs[0]);
      let streamUrlOriginal = Object.keys(pick.primitive._modelInfo)[
        pickedID - 1
      ];
      let streamUrlJSON =
        streamUrlOriginal.replace(".stream", ".json") + "?hasGeometry=false";
      axios.get(streamUrlJSON).then(function (res) {
        document.getElementById("bubbleTableBody").innerHTML = "";
        var html = "";
        var propertyDataArr = []
        if (res.data && res.data.fieldNames && res.data.fieldValues) {
          for (let i = 0; i < res.data.fieldNames.length; i++) {
            if (res.data.fieldValues[i].trim() == "") {
              continue;
            } else {
              // html +=
              //   "<tr><td style='padding-bottom: 10px;'>" +
              //   res.data.fieldNames[i] +
              //   "</td><td style='padding-bottom: 10px;'>" +
              //   res.data.fieldValues[i] +
              //   "</td></tr>";
              propertyDataArr.push(
                {
                  caption: res.data.fieldNames[i],
                  value: res.data.fieldValues[i]
                },
              )
            }
          }
          store.commit('updatepropertyWindowShowState', true)
          store.commit('updateClickPropertyData', propertyDataArr)
          // document.getElementById("bubbleTableBody").innerHTML = html;
          // document.getElementById("bubble").style.display = "block";

          //绑定气泡关闭事件
          // document.getElementById("bubble").onclick = () => {
          //   document.getElementById("bubble").style.display = "none";
          // }
        }
      });
    }
  }
  //S3M查询（BIM）
  async function searchS3MFeatureInfo(layerName, IDs) {
    let layerDic = null;
    var dataName = null;
    let userPermissonFields;
    debugger
    if (layerName.indexOf("FINAL_CYKJ") > -1) {
      dataName = Window.udbname + ":" + layerName + "_TABLE";
      store.commit("updateBubbleLayerName", 'BIM属性');
    }
    else if (layerName.indexOf("CYKJ_") > -1) {
      dataName = Window.udbname + ":" + layerName + "_TABLE";
      store.commit("updateBubbleLayerName", 'BIM属性');
    }

    var dataName;

    var datasetNames = [];

    datasetNames.push(dataName);
    var idsParam = new SuperMap.GetFeaturesByIDsParameters({
      IDs: IDs,
      datasetNames: datasetNames,
    });
    var url = Window.searchUrl;
    var features = null;
    var el = null;

    var queryPara = {
      getFeatureMode: "ID",
      datasetNames: datasetNames,
      "ids": IDs,
      maxFeatures: 100000,
    };
    var queryStr = JSON.stringify(queryPara);
    axios
      .post(
        url + "/featureResults.json?returnContent=true",
        queryStr
      )
      .then(function (res) {
        debugger
        var data = res.data
        document.getElementById("bubbleTableBody").innerHTML = "";
        if (data.totalCount > 0) {
          var features = data.features[0]
          let featurePropertiesKey =
            features.fieldNames;
          let featurePropertiesValue =
            features.fieldValues;
          var propertyDataArr = []
          for (let i = 0; i < featurePropertiesKey.length; i++) {
            let key = featurePropertiesKey[i]
            if (
              key.indexOf('IFC') > -1 ||
              key == "SHAPE_LENG" ||
              key == "SMGEOMETRY" ||
              key.indexOf("SMID") > -1 ||
              key.indexOf('SM') > -1 ||
              key == "SMLENGTH" ||
              key == "SMTOPOERROR" ||
              key == "SMUSERID" ||
              key == "SMSDRIW" ||
              key == "SMSDRIN" ||
              key == "SMSDRIE" ||
              key == "SMSDRIS" ||
              key == "SMUSERID" ||
              key == "SMLIBTILEID" ||
              key == "SMGEOMETRYSIZE" ||
              key == "SMGEOPOSITION" ||
              key == "FIELD_SMLENGTH" ||
              key == "FIELD_SMGEOMETRY" ||
              key == "FIELD_SMTOPOERROR" ||
              key == "FIELD_SMFNODE" ||
              key == "FIELD_SMTNODE" ||
              key == "FIELD_SMEDGEID" ||
              key == "FIELD_SMRESISTANCEA" ||
              key == "FIELD_SMRESISTANCEB" ||
              key == "FIELD_SMID" ||
              key == "SMUSERID_1" ||
              key == "SMPPOINT" ||
              key == "SMNPOINT" ||
              key == "OBJECTID" ||
              key == "SHAPE_LEN" ||
              key == "ELEMENTID" ||
              key == "CATEGORYID" ||
              key == "CATEGORYNAME" ||
              key == "TYPEID" ||
              key == "TYPENAME" ||
              key == "UNIQUEID" ||
              key == "ELEMENTNAME" ||
              key == "DOCUMENTTITLE" ||
              key == "GROUPID" ||
              key == "INTERIORFACESAREA" ||
              key == "EXTERIORFACESAREA" ||
              key.indexOf('FIELD_') > -1 ||
              key == "VOLUME" ||
              key == "SURFACEAREA" ||
              key == "VERTEXCOUNT" ||
              key.indexOf("TUDE") > -1
            ) {
              continue;
            } else {
              if (featurePropertiesValue[i].trim() == "") {
                continue;
              } else {
                propertyDataArr.push(
                  {
                    caption: key,
                    value: featurePropertiesValue[i]
                  },
                )
              }
            }
          }
          store.commit('updatepropertyWindowShowState', true)
          store.commit('updateClickPropertyData', propertyDataArr)

        }
        store.commit("updatepropertyDIVLoadingState", false);
      })
      .catch(function (e) {
        // ElMessage.error("查询数据失败！");
        console.log(e)
        store.commit("updatepropertyDIVLoadingState", false);
      });

  }
  // 地形深度
  // viewer.scene.postProcessStages.fxaa.enabled = false
  viewer.scene.globe.depthTestAgainstTerrain = true;
  window.viewer = viewer;
  window.scene = viewer.scene;
  var scene = viewer.scene;
  //得到当前三维场景的椭球体
  var ellipsoid = scene.globe.ellipsoid;
  let cartesian = null;
  // 定义当前场景的画布元素的事件处理
  var positionhandler = new Cesium.ScreenSpaceEventHandler(scene.canvas);
  //设置鼠标移动事件的处理函数，这里负责监听x,y坐标值变化
  positionhandler.setInputAction(function (movement) {
    //通过指定的椭球或者地图对应的坐标系，将鼠标的二维坐标转换为对应椭球体三维坐标
    cartesian = viewer.camera.pickEllipsoid(movement.endPosition, ellipsoid);
    if (cartesian) {
      //将笛卡尔坐标转换为地理坐标
      var cartographic = ellipsoid.cartesianToCartographic(cartesian);
      //将弧度转为度的十进制度表示
      document.getElementById("longitudeValue").innerHTML =
        "经度：" + Cesium.Math.toDegrees(cartographic.longitude).toFixed(4);
      document.getElementById("latitudeValue").innerHTML =
        "纬度：" + Cesium.Math.toDegrees(cartographic.latitude).toFixed(4);
      //获取相机高度
      document.getElementById("heightValue").innerHTML =
        "高度:" + Math.ceil(viewer.camera.positionCartographic.height) + "米";
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  //设置鼠标滚动事件的处理函数，这里负责监听高度值变化
  positionhandler.setInputAction(function (wheelment) {
    document.getElementById("heightValue").innerHTML =
      "高度:" + Math.ceil(viewer.camera.positionCartographic.height) + "米";
  }, Cesium.ScreenSpaceEventType.WHEEL);

  // 对Date的扩展，将 Date 转化为指定格式的String
  // 月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
  // 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
  // 例子：
  // (new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423
  // (new Date()).Format("yyyy-M-d h:m:s.S")      ==> 2006-7-2 8:9:4.18
  Date.prototype.Format = function (fmt) {
    var o = {
      'M+': this.getMonth() + 1, //月份
      'd+': this.getDate(), //日
      'H+': this.getHours(), //小时
      'h+': this.getHours(), //小时
      'm+': this.getMinutes(), //分
      's+': this.getSeconds(), //秒
      'q+': Math.floor((this.getMonth() + 3) / 3), //季度
      S: this.getMilliseconds(), //毫秒
    };
    if (/(y+)/.test(fmt))
      fmt = fmt.replace(
        RegExp.$1,
        (this.getFullYear() + '').substr(4 - RegExp.$1.length),
      );
    for (var k in o)
      if (new RegExp('(' + k + ')').test(fmt))
        fmt = fmt.replace(
          RegExp.$1,
          RegExp.$1.length == 1
            ? o[k]
            : ('00' + o[k]).substr(('' + o[k]).length),
        );
    return fmt;
  };
}

export default initViewer;
