<template>
  <SuWindow
    :title="props.title"
    :show="props.show"
    :id="props.id"
    :data="props.data"
    class="qd-panel"
    height="auto"
  >
    <el-row class="myRow">
      <el-col :span="24">
        <el-tree
          ref="treeRef"
          :data="treeData"
          show-checkbox
          node-key="name"
          :default-expanded-keys="treeDefaultExpandKeys"
          :props="treeProps"
          custom-class="db_tree"
          style="height: auto; overflow-y: auto"
          @check="handleTreeCheck"
          :default-checked-keys="['实景三维2024', '影像地图']"
        ></el-tree>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script setup>
import axios from "axios";
import store from "@/store";
import { ElMessage, ElTree, ElLoading } from "element-plus";
import MapLayerUtil from "@/components/common/class/MapLayerUtil";
import { ref, defineEmits, watch, nextTick } from "vue";
import MapIserverUtil from "@/components/common/class/MapIserverUtil";
import LayerController from "@/components/common/class/LayerController";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
import emitter from "@/utils/mitt.js";
const emits = defineEmits(["changeClick"]);
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: {},
  },
});

const treeProps = {
  children: "children",
  label: "name",
};

const treeData = ref();
const treeRef = ref();
const treeDefaultExpandKeys = ref(["三维场景"]);
const layerTransparent = ref(0);
const showPipeInfo = ref(false);

const handleNodeCheck = (data, checked, leafCheck) => {};

watch(
  () => props.show,
  function (val) {
    if (val) {
      //如果展示
      // 判断id
      if (props.id == "basescene") {
        if (store.state.layers.layerTreeState.length == 0) {
          let pipeTree = store.state.layers.gxqPipeDic;
          treeData.value = [
            {
              id: "scene3",
              name: "三维场景",
              label: "三维场景",
              ifShowCheckbox: false,
              children: [
                {
                  id: "shijingsanwei",
                  icon: "icon-diqiu-copy",
                  name: "实景三维2024",
                  label: "实景三维2024",
                  type: "3d",
                },
                {
                  id: "shijingsanwei2021",
                  icon: "icon-diqiu-copy",
                  name: "实景三维2021",
                  label: "实景三维2021",
                  type: "3d",
                },
                {
                  id: "guihuaMAX",
                  icon: "icon-diqiu-copy",
                  name: "规划设计场景",
                  label: "规划设计场景",
                  type: "3d",
                },
                {
                  id: "DEM",
                  icon: "icon-diqiu-copy",
                  name: "数字高程模型",
                  label: "数字高程模型",
                  type: "3d",
                },
              ],
            },
            {
              id: "scene2",
              name: "二维场景",
              label: "二维场景",
              ifShowCheckbox: false,
              children: [
                {
                  id: "yxmap",
                  icon: "icon-yaogancehui",
                  name: "影像地图",
                  label: "影像地图",
                  type: "2d",
                },
                {
                  id: "yxmap2024",
                  icon: "icon-yaogancehui",
                  name: "影像地图2024",
                  label: "影像地图2024",
                  type: "2d",
                },
                {
                  id: "dzmap",
                  icon: "icon-ditu",
                  name: "电子地图",
                  label: "电子地图",
                  type: "2d",
                },
              ],
            },
            {
              id: "sceneDZ",
              name: "地质三维",
              label: "地质三维",
              ifShowCheckbox: false,
              children: [
                {
                  id: "GIM_010",
                  icon: "icon-yaogancehui",
                  name: "010地块",
                  label: "GIM_010_实物纹理",
                },
                {
                  id: "GIM_011",
                  icon: "icon-yaogancehui",
                  name: "011地块",
                  label: "GIM_011_实物纹理",
                },
                {
                  id: "GIM_014",
                  icon: "icon-yaogancehui",
                  name: "014地块",
                  label: "GIM_014_实物纹理",
                },
                {
                  id: "GIM_016",
                  icon: "icon-yaogancehui",
                  name: "016地块",
                  label: "GIM_016_实物纹理",
                },
                {
                  id: "GIM_020",
                  icon: "icon-yaogancehui",
                  name: "020地块",
                  label: "GIM_020_实物纹理",
                },
              ],
            },
          ];
          store.state.layers.layerTreeState = treeData.value;
        } else {
          treeData.value = store.state.layers.layerTreeState;
        }
      }
      nextTick(() => {
        let treeNodes = treeRef.value.el$.querySelectorAll(
          ".el-tree-node__content"
        );
        treeNodes.forEach((nodeItem) => {
          const nodeData = nodeItem.closest(".el-tree-node").dataset.key;
          const currentNode = treeData.value
            .flat(Infinity)
            .find((node) => node.name === nodeData);

          if (currentNode && currentNode.ifShowCheckbox === false) {
            nodeItem.classList.add("hide-checkbox");
          }
        });
      });
    } else {
      showPipeInfo.value = false;
    }
  }
);

watch(
  () => store.state.scrollShow,
  function (val) {
    if (val == true) {
      handleScene3Check(false);
    } else {
      store.state.scene3cmList.map((item) => {
        viewer.scene.layers.find(item).splitDirection = 0;
        console.log(viewer.scene.layers.find(item));
      });
    }
  },
  {
    deep: true,
  }
);
//倾斜摄影check
const handleScene3Check = (checked) => {
  // let layernameArr = store.state.gaoxinquLayerArr;
  if (store.state.scene3cmList.length == 0) {
    let gxqQingxiePromise = viewer.scene.open(
      store.state.layer_url.s3mLayer,
      undefined,
      {
        autoSetView: false,
      }
    );
    gxqQingxiePromise.then(function (layer) {
      store.state.scene3cmList = [];
      layers.map((item) => {
        item.maxVisibleAltitude = store.state.s3mMaxVisibleAltitude; //可视高度设置
        store.state.scene3cmList.push(item.name);
      });
    });
  } else {
    if (store.state.scene3cmList && store.state.scene3cmList.length > 0) {
      store.state.scene3cmList.map((item) => {
        viewer.scene.layers.find(item).visible = checked;
        viewer.scene.layers.find(item).maxVisibleAltitude =
          store.state.s3mMaxVisibleAltitude; //可视高度设置
      });
    }
  }
};
//添加字段
const addFiled = (res) => {
  let that = this;
  selectField.value = res.data.fieldNames;
};

//根据树添加到select中
function getAllNodesToSelect(treeData) {
  for (let item of treeData) {
    if (!item.children) {
      selectLayer.value.push(item);
    } else {
      getAllNodesToSelect(item.children);
    }
  }
}
const handleTreeCheck = (node, treeStates) => {
  let checked = treeStates.checkedKeys.includes(node.name);
  switch (node.id) {
    case "BIM":
      handleBIMCheck(checked);
      break;
  }
  switch (node.name) {
    case "实景三维2024":
      handleJianChang2024Scene3Check(checked);
      break;
    case "实景三维2021":
      handleJianChang2021Scene15Check(checked);
      break;
    case "规划三维":
      handleGHMXScene3Check(checked);
      break;
    case "规划设计场景":
      handleSceneModel(checked);
      break;
    case "数字高程模型":
      handleSceneDEMCheck(checked);
      break;
    case "影像地图":
      handleMapSatelliteCheck(checked);
      break;
    case "影像地图2024":
      handleMapSatellite2024Check(checked);
      break;
    case "电子地图":
      handleMapDigitalCheck(checked);
      break;
    case "地质三维":
      handleDZMXCheck(checked);
      break;
    case "020地块":
      handleDZCheck(checked, node.label);
      break;
    case "010地块":
      handleDZCheck(checked, node.label);
      break;
    case "011地块":
      handleDZCheck(checked, node.label);
      break;
    case "014地块":
      handleDZCheck(checked, node.label);
      break;
    case "016地块":
      handleDZCheck(checked, node.label);
      break;
  }
  if (node.type == "pipe") {
    if (checked == true) {
      showPipeInfo.value = true;
      // drawchart_();
    } else {
      // showPipeInfo.value=false;
    }
    handlePipeCheck(checked, node);
  }
};

//BIM模型
const handleBIMCheck = (checked) => {
  if (store.state.layers.BIMLayers == null) {
    let BIMLayersPromise = viewer.scene.open(
      store.state.layer_url.BIMLayer,
      undefined,
      {
        autoSetView: false,
      }
    );
    BIMLayersPromise.then(function (layers) {
      store.state.layers.BIMLayers = [];
      layers.map((item) => {
        item.indexedDBSetting.isGeoTilesRootNodeSave = true;
        item.residentRootTile = true;
        store.state.layers.BIMLayers.push(item.name);
      });
    });
  } else {
    if (
      store.state.layers.BIMLayers &&
      store.state.layers.BIMLayers.length > 0
    ) {
      store.state.layers.BIMLayers.map((item) => {
        viewer.scene.layers.find(item).visible = checked;
      });
    }
  }
};
//规划模型
const handleGHMXScene3Check = (checked) => {
  // let layernameArr = store.state.gaoxinquLayerArr;
  if (store.state.layers.gxqJingMo2023 == null) {
    let gxqJingMoPromise = viewer.scene.open(
      store.state.layer_url.gxqqJingMo2023_url,
      undefined,
      {
        autoSetView: false,
      }
    );
    gxqJingMoPromise.then(function (layers) {
      store.state.gaoxinquJingMo2023LayerArr = [];
      layers.map((item) => {
        item.indexedDBSetting.isGeoTilesSave = true;
        item.residentRootTile = true;
        store.state.gaoxinquJingMo2023LayerArr.push(item.name);
      });
    });
    store.state.layers.gxqJingMo2023 = gxqJingMoPromise;
  } else {
    if (
      store.state.gaoxinquJingMo2023LayerArr &&
      store.state.gaoxinquJingMo2023LayerArr.length > 0
    ) {
      store.state.gaoxinquJingMo2023LayerArr.map((item) => {
        viewer.scene.layers.find(item).visible = checked;
      });
    }
  }
};
//倾斜摄影2024check
const handleJianChang2024Scene3Check = (checked) => {
  // let layernameArr = store.state.gaoxinquLayerArr;
  if (store.state.scene3cmList == null) {
    let QingxiePromise = viewer.scene.open(
      store.state.layer_url.s3mLayer,
      undefined,
      {
        autoSetView: false,
      }
    );
    QingxiePromise.then(function (layers) {
      store.state.scene3cmList = [];
      layers.map((item) => {
        store.state.scene3cmList.push(item.name);
      });
    });
    store.state.layers.qingxieLayer = QingxiePromise;
  } else {
    console.log(store.state.scene3cmList);
    if (store.state.scene3cmList && store.state.scene3cmList.length > 0) {
      store.state.scene3cmList.map((item) => {
        viewer.scene.layers.find(item).visible = checked;
      });
    }
  }
};
//倾斜摄影2021check
const handleJianChang2021Scene15Check = (checked) => {
  debugger;
  if (store.state.scene15cm2021List == null) {
    let QingxiePromise = viewer.scene.open(
      store.state.layer_url.s3mLayer2021,
      undefined,
      {
        autoSetView: false,
      }
    );
    QingxiePromise.then(function (layers) {
      store.state.scene15cm2021List = [];
      layers.map((item) => {
        store.state.scene15cm2021List.push(item.name);
      });
    });
    store.state.layers.qingxieLayer = QingxiePromise;
  } else {
    console.log(store.state.scene15cm2021List);
    if (
      store.state.scene15cm2021List &&
      store.state.scene15cm2021List.length > 0
    ) {
      store.state.scene15cm2021List.map((item) => {
        viewer.scene.layers.find(item).visible = checked;
      });
    }
  }
};
//精细模型check
const handleSceneModel = (checked) => {
  if (store.state.layers.MAXLayers == null) {
    let MAXPromise = viewer.scene.open(store.state.layer_url.MAXLayer);
    MAXPromise.then(function (layers) {
      if (layers && layers.length > 0) {
        store.state.layers.MAXLayers = [];
        for (let i = 0; i < layers.length; i++) {
          layers[i].indexedDBSetting.isGeoTilesSave = true;
          layers[i].residentRootTile = true;
          store.state.layers.MAXLayers.push(layers[i].name);
        }
      }
    });
  } else {
    store.state.layers.MAXLayers.map((item) => {
      viewer.scene.layers.find(item).visible = checked;
    });
  }
};
//树形组件三维场景隐藏将其他图层自动设置为未勾选的函数
//@layer:当前的layername
const autoSetUncheckScene3 = (layername) => {
  let checkedNodes = treeRef.value.getCheckedNodes();
  checkedNodes.map((item) => {
    if (item.name != layername && item.type == "3d") {
      treeRef.value.setChecked(item.name, false, false);
    }
  });
};
//影像地图2023check
const handleMapSatelliteCheck = (checked) => {
  if (checked) {
    LayerController.justShowImage();
  } else {
    LayerController.justHideImage();
  }
};
const handleMapSatellite2024Check = (checked) => {
  if (checked) {
    LayerController.showImage2024();
  } else {
    LayerController.hideImage2024();
  }
};
//电子地图2023
const handleMapDigitalCheck = (checked) => {
  debugger;
  if (checked) {
    LayerController.justShowEle();
  } else {
    LayerController.justHideEle();
  }
};
//地下管网check
const handlePipeCheck = (checked, node) => {
  if (node.name == "地下管网") {
    gxqPipeFun(checked);
  } else {
    if (node.children) {
      for (let i = 0; i < node.children.length; i++) {
        if (node.children[i].layers) {
          node.children[i].layers.forEach(
            (item) => (viewer.scene.layers.find(item).visible = checked)
          );
        }
      }
    } else {
      if (node.layers) {
        node.layers.forEach(
          (item) => (viewer.scene.layers.find(item).visible = checked)
        );
      }
    }
  }
};
//高新区管线控制
const gxqPipeFun = (checked) => {
  if (store.state.layers.gxqPipe == null) {
    let gxqPipePromise = viewer.scene.open(
      store.state.layer_url.gxq_guanxians3m_url,
      undefined,
      {
        autoSetView: false,
      }
    );
    gxqPipePromise.then(function (layers) {
      store.state.layers.gxqPipe = [];
      for (let i = 0; i < layers.length; i++) {
        store.state.layers.gxqPipe.push(layers[i].name);
        if (
          layers[i].name == "YS_Y_Net@GXQ_GX" ||
          layers[i].name == "YS_F_Net@GXQ_GX" ||
          layers[i].name == "WS_Y_Net@GXQ_GX" ||
          layers[i].name == "AQ_F_Net@GXQ_GX"
        ) {
          layers[i].textureUVSpeed = new Cesium.Cartesian2(-1, 0);
        }
      }
    });
    // Cesium.when(gxqPipePromise,function(layers){
    //   debugger
    // })
  } else {
    if (store.state.layers.gxqPipe && store.state.layers.gxqPipe.length > 0) {
      store.state.layers.gxqPipe.map((item) => {
        viewer.scene.layers.find(item).visible = checked;
      });
    }
  }
};

//地质模型
const handleDZMXCheck = (checked) => {
  handleDZCheck(checked);
};
//地质模型
const handleDZCheck = (checked, datasetsName) => {
  if (checked) {
    const loading = ElLoading.service({
      target: document.body,
      fullscreen: true,
      lock: false,
      text: "加载中...",
      background: "rgba(0,0,0,0.7)",
    });
    if (
      store.state.layers.sceneDZT[datasetsName] == null ||
      store.state.layers.sceneDZT[datasetsName] == undefined
    ) {
      let models = [],
        solidModelsProfile;
      solidModelsProfile = new Cesium.SolidModelsProfile(viewer.scene);
      let param = {
        datasetNames: ["GIM_JC:" + datasetsName + "_TABLE"],
        getFeatureMode: "SQL",
        queryParameter: {
          attributeFilter: "SMID > 0",
        },
      };
      axios
        .request({
          url:
            store.state.iserverHostUrl +
            "/iserver/services/data-JiaoZhouWanKeChuangXinQu/rest/data/featureResults.json?returnContent=true",
          method: "post",
          data: param,
        })
        .then((serviceResult) => {
          var features = serviceResult.data.features;
          for (var i = 0; i < features.length; i++) {
            debugger;
            //fieldValues 的SMID的值为数组中idnex为0的位置
            let SMID = features[i].fieldValues[0];
            let model = {
              id: SMID,
              model:
                store.state.iserverHostUrl +
                "/iserver/services/data-JiaoZhouWanKeChuangXinQu/rest/data/datasources/GIM_JC/datasets/" +
                datasetsName +
                "/features/" +
                SMID +
                ".stream",
            };
            models.push(model);
          }
          solidModelsProfile.addModels(models);
          solidModelsProfile.addedEvent.addEventListener((param) => {
            console.log(param);
            for (let i = 0; i < models.length; i++) {
              let curInstance =
                solidModelsProfile._s3mInstanceCollection._group[
                  models[i].model
                ].instances._array[0];
              let enu = Cesium.Transforms.eastNorthUpToFixedFrame(
                curInstance.position,
                Cesium.Ellipsoid.WGS84,
                new Cesium.Matrix4()
              );
              let offset = new Cesium.Cartesian3(0, 0, -10);
              let newPos = Cesium.Matrix4.multiplybyPoint(
                enu,
                offset,
                new Cesium.Cartesian3()
              );
              curInstance.updatePosition(newPos);
            }
          });
          //将地质体下沉

          store.state.layers.sceneDZT[datasetsName] = solidModelsProfile;
          store.state.models.dzmodelsMap[datasetsName] = solidModelsProfile;
          setTimeout(() => {
            loading.close();
          }, 1500);
        });
    } else {
    }
  } else {
    if (store.state.layers.sceneDZT[datasetsName]) {
      store.state.layers.sceneDZT[datasetsName].clear();
      store.state.layers.sceneDZT[datasetsName] = null;
      store.state.models.dzmodelsMap[datasetsName] = null;
    }
  }
};
//控制几个二维底图check状态，其中一个显示时，隐藏其他两个
const mapCheck = (layername) => {
  debugger;
  if (layername == "影像地图") {
    nextTick(function () {
      treeRef.value.setChecked("影像地图", true, false);
      treeRef.value.setChecked("电子地图", false, false);
    });
  } else if (layername == "电子地图") {
    nextTick(function () {
      treeRef.value.setChecked("电子地图", true, false);
      treeRef.value.setChecked("影像地图", false, false);
    });
  } else if (layername == "影像地图2024") {
    nextTick(function () {
      // treeRef.value.setChecked("影像地图2024", true, false);
    });
  }
};
emitter.on("baseMapCheck", mapCheck);
//dem 基础地形 check
const handleSceneDEMCheck = (checked) => {
  if (checked) {
    viewer.terrainProvider = new Cesium.CesiumTerrainProvider({
      url: store.state.layer_url.dem_scene_url,
      isSct: true,
    }); //地形服务源自SuperMap iServer发布时需设置isSct为true });
  } else {
    viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider({});
  }
};
</script>

<style lang="scss" scoped>
.su-main-right {
  position: absolute;
  z-index: 9;
  top: 1.5rem;
  right: 1.11111rem;
}

.index-line-chart {
  height: 50%;
}

.el-tree {
  background: #ffffff00 !important;
  color: #ebecee !important;
  --el-tree-node-hover-bg-color: #5de7d445;
}

.el-checkbox {
  --el-checkbox-checked-bg-color: #369ef0 !important;
  --el-checkbox-checked-input-border-color: #ffffff00 !important;
}

.myRow {
  margin-top: 10px;
}

.myIcon:before {
  color: #ffffff;
}

.myIcon {
  font-size: 18px;
  color: white;
}

.geoIcon:before {
  color: #369ef0;
}

.geoIcon {
  font-size: 16px;
  color: white;
}

.el-tabs__nav {
  width: 100%;
}

.el-tabs__item {
  width: 50%;
  color: white;
}

.windowItem .el-form-item__label {
  color: white !important;
  font-weight: 500;
}

.windowItem {
  margin-top: 2px;
}

.el-tabs__item.is-active {
  color: linear-gradient(90deg, #48bcff 0%, #369afa 100%) !important;
}

.el-tabs__active-bar {
  background-color: linear-gradient(90deg, #48bcff 0%, #369afa 100%) !important;
}

.el-tabs__item:hover {
  color: linear-gradient(90deg, #48bcff 0%, #369afa 100%) !important;
  // font-size: 18px;
}

.clearBtn {
  --el-button-hover-bg-color: linear-gradient
    (108.25deg, #5ac1ff 0.74%, #0c75e0 101.33%) !important;
  background-color: rgba(8, 18, 45, 0.6) !important;
  border-color: white !important;
  color: white !important;
  border-radius: 5px;

  &:hover {
    background: linear-gradient(
      108.25deg,
      #5ac1ff 0.74%,
      #0c75e0 101.33%
    ) !important;
  }
}

.is-checked {
  .el-radio__label {
    .geoIcon {
      color: #24ffcb;
    }
  }
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: #24ffcb;
  background: #24ffcb;
}

.el-input {
  --el-input-text-color: #ffffff;
  --el-input-focus-border: #24ffcb;
  --el-input-bg-color: #ffffff00;
  --el-input-focus-border-color: #24ffcb;
}

.el-tree .el-tree-node .is-leaf + .el-checkbox .el-checkbox__inner {
  display: inline-block;
}

.el-tree .el-tree-node .el-checkbox .el-checkbox__inner {
  display: none;
}

.pipe-window {
  background: rgba(16, 27, 55, 0.8);
  opacity: 0.8;
  border-radius: 15px !important;
  backdrop-filter: blur(15px);
  box-shadow: 10px 10px 30px 5px rgba(0, 0, 0, 0.15);
  padding: 0 15px 15px 15px;
  font-size: 14px;
  z-index: 100;
  width: 32%;

  /*
  &::before {
    content: "";
    display: block;
    height: 1px;
    width: 100%;
    background-image: linear-gradient(
      270deg,
      rgba(106, 251, 255, 0) 0%,
      #38f4ff 100%
    );
    position: absolute;
    top: 0;
    left: 0;
  }*/

  &-header {
    font-size: 18px;
    color: #ffffff;
    background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: bold;
    color: transparent;
    display: flex;
    align-items: center;
    height: 50px;

    &::after {
      content: "";
      flex: 1;
      height: 20px;
      background-image: url(../../public/images/panel-title-icon2.svg);
      background-repeat: no-repeat;
      background-size: 60% 100%;
      margin-left: 10px;
    }
  }

  &-body {
    height: calc(100% - 50px);
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;

    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }

    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }

  .legend-panel-body {
    height: 250px;
    overflow: auto;
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;

    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }

    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }
}

.pipe-window .su-panel-header {
  font-size: 18px;
  padding-top: 3px;
  padding-bottom: 3px;
}

:deep(.hide-checkbox) {
  .el-checkbox {
    display: none;
  }
}
</style>
