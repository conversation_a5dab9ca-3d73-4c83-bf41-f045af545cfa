<template>
  <SuWindow
    :title="props.title"
    :show="props.show"
    :id="props.id"
    :data="props.data"
    class="controller-panel"
    height="auto"
    width="35vh"
  >
    <el-divider border-style="" />
    <el-row style="margin-top: 5px">
      <el-col :span="12">
        <i :class="['iconfont f16  icon-fenxiaoguize myIcon']"> 航飞视频 </i>
      </el-col>
    </el-row>
    <el-divider border-style="" />
    <el-row justify="center" class="myRow">
      <el-col :span="24">
        <el-table
          :data="tableList"
          max-height="300"
          style="width: 100%; height: 300px; margin-top: 10px"
          @row-click="tableClick"
          highlight-current-row
        >
          <el-table-column prop="id" label="编号" width="70"></el-table-column>
          <el-table-column prop="name" label="名称"></el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </SuWindow>
</template>

<script setup>
import { truncate } from "lodash-es";
import axios from "axios";
import { ElMessage, ElTree } from "element-plus";
import {
  ref,
  defineEmits,
  provide,
  watch,
  handleError,
  onMounted,
  toRefs,
} from "vue";
import iserverMapLayer from "../../common/class/iserverMapLayer";
import store from "@/store";
// const emits = defineEmits(["changeClick"]);
const props = defineProps({
  title: {
    type: String,
    default: "空中漫游",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "kzmy",
  },
  data: {
    type: Object,
    default: {},
  },
});

const tableList = [
  { id: 1, name: "20240612航飞视频", src: "/data/media/20240612航飞视频.mp4" },
  { id: 2, name: "20250106航飞视频", src: "/data/media/20250106航飞视频.mp4" },
  {
    id: 3,
    name: "20250708航飞视频1",
    src: "/data/media/20250708航飞视频1.mp4",
  },
  {
    id: 4,
    name: "20250708航飞视频2",
    src: "/data/media/20250708航飞视频2.mp4",
  },
  {
    id: 5,
    name: "20250708航飞视频3",
    src: "/data/media/20250708航飞视频3.mp4",
  },
];

onMounted(() => {});

//表格点击事件
const tableClick = (row) => {
  console.log(row);
  console.log(row.src);
  debugger;
  // iserverMapLayer.flytoPoint(row.data);
  store.commit("updateVideoShow", true);
  // store.commit("updateVideoSrc", "/data/media/20240612航飞视频.mp4");
  store.commit("updateVideoSrc", row.src);
};

// const closeWindow = () => {
//   if (qjLayer.value) {
//     iserverMapLayer.removeLayer(qjLayer.value.name);
//   }
//   currentData.value = {};
//   tableList.value = [];

//   queryFieldStr.value = "";
//   // winShow.value = false;
// };

const emits = defineEmits(["changeTab"]);
const navItemController = (item) => {
  if (item.item.id == "kzmy") {
    emits("changeTab", item.item.id, false);
  }
};
provide("controllerClick", navItemController);
</script>

<style lang="scss">
.index-line-chart {
  height: 50%;
}

.myIcon {
  font-size: 16px;
  color: #ffffff;
}

.controller-panel {
  position: fixed;
  right: 3rem;
  // left: 4rem;
}

.el-timeline-item__content {
  color: white;
  font-size: 16px;
  font-weight: 500;
}

.el-timeline-item__node--primary {
  border-color: #24ffcb;
}

.el-divider--horizontal {
  margin: 0.5333rem 0;
}

.el-overlay {
  div {
    width: 100%;

    section {
      padding: 0px !important;
      overflow: hidden !important;
    }
  }
}

.el-table__body-wrapper::-webkit-scrollbar {
  height: 6px !important;
  width: 6px !important;
}

.el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: #0d233800 !important;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #ccc !important;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #cfcfcf !important;
}

.el-table {
  background-color: #ffffff00 !important;
  --el-table-bg-color: #ffffff00 !important;
  --el-table-row-hover-bg-color: #369ef0 !important;

  tr {
    background-color: #ffffff00 !important;
    color: white !important;
  }
}

.el-table th.el-table__cell {
  user-select: none;
  background-color: #ffffff00;
}

.el-table__body tr.current-row > td {
  background-color: #369ef0 !important;
}
</style>